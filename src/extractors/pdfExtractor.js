const pdfParse = require('pdf-parse');
const Tesseract = require('tesseract.js');
const fs = require('fs-extra');
const path = require('path');
const config = require('../config/config');
const { logger, logError, logProcessing } = require('../config/logger');

/**
 * Extrator de dados de arquivos PDF
 */
class PDFExtractor {
  constructor() {
    this.ocrWorker = null;
  }

  /**
   * Inicializa o worker do Tesseract para OCR
   */
  async initializeOCR() {
    if (!this.ocrWorker) {
      this.ocrWorker = await Tesseract.createWorker();
      await this.ocrWorker.loadLanguage(config.ocr.language);
      await this.ocrWorker.initialize(config.ocr.language);
      await this.ocrWorker.setParameters({
        tessedit_pageseg_mode: config.ocr.psm
      });
      logger.info('OCR worker initialized');
    }
  }

  /**
   * Finaliza o worker do OCR
   */
  async terminateOCR() {
    if (this.ocrWorker) {
      await this.ocrWorker.terminate();
      this.ocrWorker = null;
      logger.info('OCR worker terminated');
    }
  }

  /**
   * Extrai texto de um arquivo PDF
   */
  async extractText(filePath) {
    try {
      logProcessing('pdf_extraction_started', { filePath });

      const dataBuffer = await fs.readFile(filePath);
      
      // Primeiro tenta extrair texto diretamente do PDF
      const pdfData = await pdfParse(dataBuffer);
      
      if (pdfData.text && pdfData.text.trim().length > 50) {
        // PDF tem texto extraível
        logProcessing('pdf_text_extracted', {
          filePath,
          textLength: pdfData.text.length,
          method: 'direct'
        });
        
        return pdfData.text;
      } else {
        // PDF é escaneado, usa OCR
        logger.info('PDF appears to be scanned, using OCR');
        return await this.extractTextWithOCR(filePath);
      }
    } catch (error) {
      logError(error, { context: 'extractText', filePath });
      throw error;
    }
  }

  /**
   * Extrai texto usando OCR
   */
  async extractTextWithOCR(filePath) {
    try {
      await this.initializeOCR();
      
      logProcessing('ocr_extraction_started', { filePath });
      
      const { data: { text } } = await this.ocrWorker.recognize(filePath);
      
      logProcessing('ocr_extraction_completed', {
        filePath,
        textLength: text.length
      });
      
      return text;
    } catch (error) {
      logError(error, { context: 'extractTextWithOCR', filePath });
      throw error;
    }
  }

  /**
   * Extrai dados estruturados de uma invoice em PDF
   */
  async extractInvoiceData(filePath) {
    try {
      const text = await this.extractText(filePath);
      const invoiceData = this.parseInvoiceText(text);
      
      logProcessing('invoice_data_extracted', {
        filePath,
        invoiceData
      });
      
      return invoiceData;
    } catch (error) {
      logError(error, { context: 'extractInvoiceData', filePath });
      throw error;
    }
  }

  /**
   * Analisa o texto extraído para encontrar dados da invoice
   */
  parseInvoiceText(text) {
    const invoiceData = {
      invoiceNumber: null,
      invoiceDate: null,
      vendorName: null,
      lineItems: [],
      subtotal: null,
      tax: null,
      total: null,
      rawText: text
    };

    try {
      // Padrões regex para diferentes campos
      const patterns = {
        invoiceNumber: [
          /invoice\s*#?\s*:?\s*([A-Z0-9\-]+)/i,
          /inv\s*#?\s*:?\s*([A-Z0-9\-]+)/i,
          /number\s*:?\s*([A-Z0-9\-]+)/i
        ],
        invoiceDate: [
          /date\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/i,
          /invoice\s*date\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/i,
          /(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/
        ],
        vendorName: [
          /from\s*:?\s*([^\n\r]+)/i,
          /vendor\s*:?\s*([^\n\r]+)/i,
          /bill\s*to\s*:?\s*([^\n\r]+)/i
        ],
        total: [
          /total\s*:?\s*\$?(\d+\.?\d*)/i,
          /amount\s*due\s*:?\s*\$?(\d+\.?\d*)/i,
          /grand\s*total\s*:?\s*\$?(\d+\.?\d*)/i
        ],
        subtotal: [
          /subtotal\s*:?\s*\$?(\d+\.?\d*)/i,
          /sub\s*total\s*:?\s*\$?(\d+\.?\d*)/i
        ],
        tax: [
          /tax\s*:?\s*\$?(\d+\.?\d*)/i,
          /sales\s*tax\s*:?\s*\$?(\d+\.?\d*)/i
        ]
      };

      // Extrai cada campo usando os padrões
      for (const [field, regexList] of Object.entries(patterns)) {
        for (const regex of regexList) {
          const match = text.match(regex);
          if (match && match[1]) {
            invoiceData[field] = match[1].trim();
            break;
          }
        }
      }

      // Extrai itens de linha (mais complexo)
      invoiceData.lineItems = this.extractLineItems(text);

      // Usa valores padrão se não encontrados
      if (!invoiceData.vendorName) {
        invoiceData.vendorName = config.quickbooks.defaultVendorName;
      }

      // Converte valores monetários para números
      ['subtotal', 'tax', 'total'].forEach(field => {
        if (invoiceData[field]) {
          const numValue = parseFloat(invoiceData[field].replace(/[^\d.]/g, ''));
          if (!isNaN(numValue)) {
            invoiceData[field] = numValue;
          }
        }
      });

      // Formata data se encontrada
      if (invoiceData.invoiceDate) {
        invoiceData.invoiceDate = this.formatDate(invoiceData.invoiceDate);
      }

    } catch (error) {
      logError(error, { context: 'parseInvoiceText' });
    }

    return invoiceData;
  }

  /**
   * Extrai itens de linha da invoice
   */
  extractLineItems(text) {
    const lineItems = [];
    
    try {
      // Padrões para identificar linhas de itens
      const linePatterns = [
        // Padrão: Descrição Qty Rate Amount
        /^(.+?)\s+(\d+\.?\d*)\s+\$?(\d+\.?\d*)\s+\$?(\d+\.?\d*)$/gm,
        // Padrão: Descrição Amount
        /^(.+?)\s+\$?(\d+\.?\d*)$/gm
      ];

      for (const pattern of linePatterns) {
        let match;
        while ((match = pattern.exec(text)) !== null) {
          const item = {
            description: match[1].trim(),
            quantity: match[2] ? parseFloat(match[2]) : 1,
            rate: match[3] ? parseFloat(match[3]) : parseFloat(match[2] || 0),
            amount: match[4] ? parseFloat(match[4]) : parseFloat(match[2] || 0)
          };

          // Filtra itens válidos
          if (item.description.length > 3 && item.amount > 0) {
            lineItems.push(item);
          }
        }
        
        if (lineItems.length > 0) break; // Para no primeiro padrão que funcionar
      }

      // Se não encontrou itens específicos, cria um item genérico
      if (lineItems.length === 0) {
        lineItems.push({
          description: 'Angus Sprinkler Repair',
          quantity: 1,
          rate: 0,
          amount: 0
        });
      }

    } catch (error) {
      logError(error, { context: 'extractLineItems' });
    }

    return lineItems;
  }

  /**
   * Formata data para padrão consistente
   */
  formatDate(dateString) {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        // Tenta outros formatos
        const parts = dateString.split(/[\/\-]/);
        if (parts.length === 3) {
          // Assume MM/DD/YYYY ou DD/MM/YYYY
          const month = parseInt(parts[0]);
          const day = parseInt(parts[1]);
          const year = parseInt(parts[2]);
          
          if (year < 100) {
            year += 2000; // Converte anos de 2 dígitos
          }
          
          return new Date(year, month - 1, day).toISOString().split('T')[0];
        }
      }
      return date.toISOString().split('T')[0];
    } catch (error) {
      logError(error, { context: 'formatDate', dateString });
      return new Date().toISOString().split('T')[0]; // Data atual como fallback
    }
  }
}

module.exports = PDFExtractor;
