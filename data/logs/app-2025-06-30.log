{"timestamp":"2025-06-30T21:36:15.283Z","level":"info","message":"Sistema iniciado","port":"3000","zoho_configured":"**********************************************************************"}
{"timestamp":"2025-06-30T21:36:36.980Z","level":"info","message":"Status verificado","status":"running","zoho_configured":"**********************************************************************","directories":{"input":true,"output":true,"logs":true},"environment":{"node_version":"v22.17.0","port":"3000","env":"development"}}
{"timestamp":"2025-06-30T21:36:36.985Z","level":"info","message":"Listados 0 arquivos de saída"}
{"timestamp":"2025-06-30T21:37:07.060Z","level":"info","message":"Status verificado","status":"running","zoho_configured":"**********************************************************************","directories":{"input":true,"output":true,"logs":true},"environment":{"node_version":"v22.17.0","port":"3000","env":"development"}}
{"timestamp":"2025-06-30T21:37:37.058Z","level":"info","message":"Status verificado","status":"running","zoho_configured":"**********************************************************************","directories":{"input":true,"output":true,"logs":true},"environment":{"node_version":"v22.17.0","port":"3000","env":"development"}}
{"timestamp":"2025-06-30T21:38:07.066Z","level":"info","message":"Status verificado","status":"running","zoho_configured":"**********************************************************************","directories":{"input":true,"output":true,"logs":true},"environment":{"node_version":"v22.17.0","port":"3000","env":"development"}}
