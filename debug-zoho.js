#!/usr/bin/env node

/**
 * Script de debug para identificar o problema com o Zoho OAuth
 */

const https = require('https');
const querystring = require('querystring');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise(resolve => rl.question(prompt, resolve));
}

function makeHttpsRequest(options, postData) {
  return new Promise((resolve, reject) => {
    console.log('🔍 Fazendo requisição para:', options.hostname + options.path);
    console.log('📤 Dados enviados:', postData);
    
    const req = https.request(options, (res) => {
      console.log('📥 Status da resposta:', res.statusCode);
      console.log('📥 Headers da resposta:', res.headers);
      
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log('📥 Resposta completa:', data);
        try {
          const parsed = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: parsed, raw: data });
        } catch (e) {
          resolve({ statusCode: res.statusCode, data: null, raw: data, parseError: e.message });
        }
      });
    });
    
    req.on('error', (error) => {
      console.log('❌ Erro na requisição:', error);
      reject(error);
    });
    
    if (postData) req.write(postData);
    req.end();
  });
}

async function debugZohoAuth() {
  console.log('🔍 Debug do Zoho OAuth');
  console.log('======================');
  console.log('');
  
  try {
    // Obter credenciais
    const clientId = await question('Client ID: ');
    const clientSecret = await question('Client Secret: ');
    const authCode = await question('Código de autorização: ');
    
    if (!clientId || !clientSecret || !authCode) {
      console.log('❌ Todos os campos são obrigatórios!');
      process.exit(1);
    }
    
    console.log('');
    console.log('🔍 Verificando dados de entrada:');
    console.log('Client ID:', clientId.substring(0, 10) + '...');
    console.log('Client Secret:', clientSecret.substring(0, 10) + '...');
    console.log('Auth Code:', authCode.substring(0, 20) + '...');
    console.log('');
    
    // Preparar dados
    const postData = querystring.stringify({
      grant_type: 'authorization_code',
      client_id: clientId,
      client_secret: clientSecret,
      redirect_uri: 'http://localhost:3001/callback',
      code: authCode
    });
    
    const options = {
      hostname: 'accounts.zoho.com',
      path: '/oauth/v2/token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': postData.length,
        'User-Agent': 'Invoice-Automation/1.0'
      }
    };
    
    console.log('⏳ Fazendo requisição para o Zoho...');
    console.log('');
    
    const result = await makeHttpsRequest(options, postData);
    
    console.log('');
    console.log('📊 ANÁLISE DA RESPOSTA:');
    console.log('=======================');
    console.log('Status Code:', result.statusCode);
    console.log('');
    
    if (result.data) {
      console.log('📋 Dados estruturados:');
      console.log(JSON.stringify(result.data, null, 2));
      
      if (result.data.error) {
        console.log('');
        console.log('❌ ERRO IDENTIFICADO:');
        console.log('Tipo:', result.data.error);
        console.log('Descrição:', result.data.error_description || 'Não fornecida');
        
        console.log('');
        console.log('🔧 POSSÍVEIS SOLUÇÕES:');
        
        switch (result.data.error) {
          case 'invalid_code':
            console.log('- O código de autorização expirou ou é inválido');
            console.log('- Gere um novo código de autorização');
            console.log('- Certifique-se de usar o código imediatamente');
            break;
            
          case 'invalid_client':
            console.log('- Client ID ou Client Secret incorretos');
            console.log('- Verifique as credenciais no Zoho API Console');
            break;
            
          case 'invalid_grant':
            console.log('- Código já foi usado ou expirou');
            console.log('- Redirect URI não confere');
            console.log('- Gere um novo código de autorização');
            break;
            
          case 'invalid_request':
            console.log('- Parâmetros da requisição incorretos');
            console.log('- Verifique se todos os campos estão preenchidos');
            break;
            
          default:
            console.log('- Erro desconhecido:', result.data.error);
            console.log('- Tente gerar um novo código de autorização');
        }
      } else if (result.data.access_token) {
        console.log('');
        console.log('✅ SUCESSO!');
        console.log('Access Token:', result.data.access_token ? 'Recebido' : 'Não recebido');
        console.log('Refresh Token:', result.data.refresh_token ? 'Recebido' : 'Não recebido');
        console.log('Expires In:', result.data.expires_in || 'Não informado');
        
        if (result.data.refresh_token) {
          console.log('');
          console.log('🎉 Refresh Token obtido com sucesso!');
          console.log('Refresh Token:', result.data.refresh_token);
        }
      }
    } else {
      console.log('❌ Resposta não é JSON válido:');
      console.log('Resposta bruta:', result.raw);
      
      if (result.parseError) {
        console.log('Erro de parse:', result.parseError);
      }
    }
    
    console.log('');
    console.log('🔗 LINKS ÚTEIS:');
    console.log('- Zoho API Console: https://api-console.zoho.com/');
    console.log('- Documentação OAuth: https://www.zoho.com/mail/help/api/oauth-overview.html');
    console.log('');
    
    // Gerar nova URL se necessário
    const generateNew = await question('🔄 Gerar nova URL de autorização? (s/n): ');
    if (generateNew.toLowerCase() === 's') {
      const newAuthUrl = `https://accounts.zoho.com/oauth/v2/auth?scope=ZohoMail.messages.READ&client_id=${clientId}&response_type=code&redirect_uri=http://localhost:3001/callback&access_type=offline&prompt=consent`;
      console.log('');
      console.log('🔗 Nova URL de autorização (com prompt=consent):');
      console.log(newAuthUrl);
      console.log('');
      console.log('📋 Dicas:');
      console.log('- Use esta URL em uma aba privada/incógnita');
      console.log('- Certifique-se de estar logado na conta correta do Zoho');
      console.log('- Use o código imediatamente após receber');
    }
    
  } catch (error) {
    console.log('');
    console.log('❌ Erro durante debug:', error.message);
    console.log('Stack:', error.stack);
  } finally {
    rl.close();
  }
}

if (require.main === module) {
  debugZohoAuth();
}
