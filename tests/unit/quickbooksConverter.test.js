const { describe, it, expect, beforeEach, afterEach, jest } = require('@jest/globals');
const fs = require('fs-extra');
const path = require('path');
const QuickBooksConverter = require('../../src/converters/quickbooksConverter');

describe('QuickBooksConverter', () => {
  let converter;
  let testOutputDir;

  beforeEach(async () => {
    converter = new QuickBooksConverter();
    testOutputDir = path.join(__dirname, '../temp/output');
    await fs.ensureDir(testOutputDir);
  });

  afterEach(async () => {
    await fs.remove(path.join(__dirname, '../temp'));
  });

  describe('Constructor', () => {
    it('should initialize with default configuration', () => {
      expect(converter).toBeDefined();
      expect(converter.csvConverter).toBeDefined();
      expect(converter.iifConverter).toBeDefined();
    });
  });

  describe('Data Validation', () => {
    it('should validate complete invoice data', () => {
      const validData = {
        invoiceNumber: 'INV-001',
        date: '2024-01-15',
        vendor: 'Test Vendor',
        amount: 100.50,
        description: 'Test invoice',
        lineItems: [
          {
            description: 'Item 1',
            amount: 100.50,
            category: 'Office Supplies'
          }
        ]
      };

      const result = converter.validateInvoiceData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing required fields', () => {
      const invalidData = {
        invoiceNumber: 'INV-001'
        // Missing required fields
      };

      const result = converter.validateInvoiceData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(error => error.includes('date'))).toBe(true);
      expect(result.errors.some(error => error.includes('vendor'))).toBe(true);
      expect(result.errors.some(error => error.includes('amount'))).toBe(true);
    });

    it('should validate date format', () => {
      const dataWithInvalidDate = {
        invoiceNumber: 'INV-001',
        date: 'invalid-date',
        vendor: 'Test Vendor',
        amount: 100.50,
        description: 'Test invoice'
      };

      const result = converter.validateInvoiceData(dataWithInvalidDate);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('date'))).toBe(true);
    });

    it('should validate amount is positive number', () => {
      const dataWithInvalidAmount = {
        invoiceNumber: 'INV-001',
        date: '2024-01-15',
        vendor: 'Test Vendor',
        amount: -100.50,
        description: 'Test invoice'
      };

      const result = converter.validateInvoiceData(dataWithInvalidAmount);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('amount'))).toBe(true);
    });
  });

  describe('CSV Conversion', () => {
    it('should convert invoice data to CSV format', async () => {
      const invoiceData = {
        invoiceNumber: 'INV-001',
        date: '2024-01-15',
        vendor: 'Test Vendor',
        amount: 100.50,
        description: 'Test invoice',
        lineItems: [
          {
            description: 'Item 1',
            amount: 100.50,
            category: 'Office Supplies'
          }
        ]
      };

      const result = await converter.convertToQuickBooks(invoiceData, {
        format: 'csv',
        outputPath: testOutputDir
      });

      expect(result.success).toBe(true);
      expect(result.files.csv).toBeDefined();
      expect(await fs.pathExists(result.files.csv)).toBe(true);

      // Verify CSV content
      const csvContent = await fs.readFile(result.files.csv, 'utf8');
      expect(csvContent).toContain('INV-001');
      expect(csvContent).toContain('Test Vendor');
      expect(csvContent).toContain('100.50');
    });

    it('should handle multiple line items in CSV', async () => {
      const invoiceData = {
        invoiceNumber: 'INV-002',
        date: '2024-01-15',
        vendor: 'Multi Item Vendor',
        amount: 250.75,
        description: 'Multi-item invoice',
        lineItems: [
          {
            description: 'Item 1',
            amount: 150.25,
            category: 'Office Supplies'
          },
          {
            description: 'Item 2',
            amount: 100.50,
            category: 'Equipment'
          }
        ]
      };

      const result = await converter.convertToQuickBooks(invoiceData, {
        format: 'csv',
        outputPath: testOutputDir
      });

      expect(result.success).toBe(true);
      
      const csvContent = await fs.readFile(result.files.csv, 'utf8');
      expect(csvContent).toContain('Item 1');
      expect(csvContent).toContain('Item 2');
      expect(csvContent).toContain('150.25');
      expect(csvContent).toContain('100.50');
    });
  });

  describe('IIF Conversion', () => {
    it('should convert invoice data to IIF format', async () => {
      const invoiceData = {
        invoiceNumber: 'INV-001',
        date: '2024-01-15',
        vendor: 'Test Vendor',
        amount: 100.50,
        description: 'Test invoice',
        lineItems: [
          {
            description: 'Item 1',
            amount: 100.50,
            category: 'Office Supplies'
          }
        ]
      };

      const result = await converter.convertToQuickBooks(invoiceData, {
        format: 'iif',
        outputPath: testOutputDir
      });

      expect(result.success).toBe(true);
      expect(result.files.iif).toBeDefined();
      expect(await fs.pathExists(result.files.iif)).toBe(true);

      // Verify IIF content
      const iifContent = await fs.readFile(result.files.iif, 'utf8');
      expect(iifContent).toContain('!TRNS');
      expect(iifContent).toContain('!SPL');
      expect(iifContent).toContain('!ENDTRNS');
      expect(iifContent).toContain('Test Vendor');
      expect(iifContent).toContain('100.50');
    });

    it('should generate proper IIF structure', async () => {
      const invoiceData = {
        invoiceNumber: 'INV-003',
        date: '2024-01-15',
        vendor: 'IIF Test Vendor',
        amount: 200.00,
        description: 'IIF test invoice',
        lineItems: [
          {
            description: 'Test Item',
            amount: 200.00,
            category: 'Test Category'
          }
        ]
      };

      const result = await converter.convertToQuickBooks(invoiceData, {
        format: 'iif',
        outputPath: testOutputDir
      });

      const iifContent = await fs.readFile(result.files.iif, 'utf8');
      const lines = iifContent.split('\n').filter(line => line.trim());

      // Check IIF structure
      expect(lines[0]).toContain('!TRNS');
      expect(lines[1]).toContain('!SPL');
      expect(lines[2]).toContain('!ENDTRNS');
      
      // Should have transaction and split lines
      const trnsLines = lines.filter(line => line.startsWith('TRNS'));
      const splLines = lines.filter(line => line.startsWith('SPL'));
      const endtrnsLines = lines.filter(line => line.startsWith('ENDTRNS'));

      expect(trnsLines.length).toBeGreaterThan(0);
      expect(splLines.length).toBeGreaterThan(0);
      expect(endtrnsLines.length).toBeGreaterThan(0);
    });
  });

  describe('Both Formats Conversion', () => {
    it('should convert to both CSV and IIF formats', async () => {
      const invoiceData = {
        invoiceNumber: 'INV-004',
        date: '2024-01-15',
        vendor: 'Both Formats Vendor',
        amount: 300.00,
        description: 'Both formats test',
        lineItems: [
          {
            description: 'Test Item',
            amount: 300.00,
            category: 'Test Category'
          }
        ]
      };

      const result = await converter.convertToQuickBooks(invoiceData, {
        format: 'both',
        outputPath: testOutputDir
      });

      expect(result.success).toBe(true);
      expect(result.files.csv).toBeDefined();
      expect(result.files.iif).toBeDefined();
      expect(await fs.pathExists(result.files.csv)).toBe(true);
      expect(await fs.pathExists(result.files.iif)).toBe(true);
    });
  });

  describe('Batch Conversion', () => {
    it('should convert multiple invoices in batch', async () => {
      const invoices = [
        {
          invoiceNumber: 'BATCH-001',
          date: '2024-01-15',
          vendor: 'Batch Vendor 1',
          amount: 100.00,
          description: 'Batch invoice 1',
          lineItems: [{ description: 'Item 1', amount: 100.00, category: 'Category 1' }]
        },
        {
          invoiceNumber: 'BATCH-002',
          date: '2024-01-16',
          vendor: 'Batch Vendor 2',
          amount: 200.00,
          description: 'Batch invoice 2',
          lineItems: [{ description: 'Item 2', amount: 200.00, category: 'Category 2' }]
        }
      ];

      const result = await converter.convertBatch(invoices, {
        format: 'csv',
        outputPath: testOutputDir
      });

      expect(result.success).toBe(true);
      expect(result.summary.total).toBe(2);
      expect(result.summary.successful).toBe(2);
      expect(result.summary.failed).toBe(0);
      expect(result.files).toHaveLength(2);
    });

    it('should handle batch conversion with some failures', async () => {
      const invoices = [
        {
          invoiceNumber: 'BATCH-003',
          date: '2024-01-15',
          vendor: 'Valid Vendor',
          amount: 100.00,
          description: 'Valid invoice',
          lineItems: [{ description: 'Item 1', amount: 100.00, category: 'Category 1' }]
        },
        {
          invoiceNumber: 'BATCH-004',
          // Missing required fields - should fail
          description: 'Invalid invoice'
        }
      ];

      const result = await converter.convertBatch(invoices, {
        format: 'csv',
        outputPath: testOutputDir
      });

      expect(result.success).toBe(false); // Overall batch failed due to some failures
      expect(result.summary.total).toBe(2);
      expect(result.summary.successful).toBe(1);
      expect(result.summary.failed).toBe(1);
      expect(result.errors).toHaveLength(1);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid output path', async () => {
      const invoiceData = {
        invoiceNumber: 'INV-005',
        date: '2024-01-15',
        vendor: 'Test Vendor',
        amount: 100.00,
        description: 'Test invoice',
        lineItems: [{ description: 'Item 1', amount: 100.00, category: 'Category 1' }]
      };

      const result = await converter.convertToQuickBooks(invoiceData, {
        format: 'csv',
        outputPath: '/invalid/path/that/does/not/exist'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle invalid format', async () => {
      const invoiceData = {
        invoiceNumber: 'INV-006',
        date: '2024-01-15',
        vendor: 'Test Vendor',
        amount: 100.00,
        description: 'Test invoice',
        lineItems: [{ description: 'Item 1', amount: 100.00, category: 'Category 1' }]
      };

      const result = await converter.convertToQuickBooks(invoiceData, {
        format: 'invalid-format',
        outputPath: testOutputDir
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('File Naming', () => {
    it('should generate unique filenames', async () => {
      const invoiceData = {
        invoiceNumber: 'INV-007',
        date: '2024-01-15',
        vendor: 'Test Vendor',
        amount: 100.00,
        description: 'Test invoice',
        lineItems: [{ description: 'Item 1', amount: 100.00, category: 'Category 1' }]
      };

      const result1 = await converter.convertToQuickBooks(invoiceData, {
        format: 'csv',
        outputPath: testOutputDir
      });

      const result2 = await converter.convertToQuickBooks(invoiceData, {
        format: 'csv',
        outputPath: testOutputDir
      });

      expect(result1.files.csv).not.toBe(result2.files.csv);
      expect(await fs.pathExists(result1.files.csv)).toBe(true);
      expect(await fs.pathExists(result2.files.csv)).toBe(true);
    });
  });
});
