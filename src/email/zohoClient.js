const axios = require('axios');
const config = require('../config/config');
const { logger, logError, logProcessing } = require('../config/logger');

/**
 * Cliente para interação com a API do Zoho Mail
 */
class ZohoMailClient {
  constructor() {
    this.accessToken = null;
    this.tokenExpiry = null;
    this.baseURL = config.zoho.apiBaseUrl;
  }

  /**
   * Obtém um novo access token usando o refresh token
   */
  async refreshAccessToken() {
    try {
      const response = await axios.post('https://accounts.zoho.com/oauth/v2/token', null, {
        params: {
          refresh_token: config.zoho.refreshToken,
          client_id: config.zoho.clientId,
          client_secret: config.zoho.clientSecret,
          grant_type: 'refresh_token'
        }
      });

      this.accessToken = response.data.access_token;
      this.tokenExpiry = Date.now() + (response.data.expires_in * 1000);

      logger.info('Zoho access token refreshed successfully');
      return this.accessToken;
    } catch (error) {
      logError(error, { context: 'refreshAccessToken' });
      throw new Error(`Failed to refresh Zoho access token: ${error.message}`);
    }
  }

  /**
   * Verifica se o token atual é válido
   */
  async ensureValidToken() {
    if (!this.accessToken || Date.now() >= this.tokenExpiry - 60000) { // Renova 1 minuto antes de expirar
      await this.refreshAccessToken();
    }
  }

  /**
   * Faz uma requisição autenticada para a API do Zoho
   */
  async makeRequest(endpoint, params = {}) {
    await this.ensureValidToken();

    try {
      const response = await axios.get(`${this.baseURL}${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        params
      });

      return response.data;
    } catch (error) {
      logError(error, { context: 'makeRequest', endpoint, params });
      throw error;
    }
  }

  /**
   * Lista e-mails com filtros específicos
   */
  async getEmails(options = {}) {
    const params = {
      limit: options.limit || 50,
      start: options.start || 0,
      sortBy: options.sortBy || 'receivedTime',
      sortOrder: options.sortOrder || 'desc'
    };

    // Adiciona filtros se especificados
    if (options.fromEmail) {
      params.fromEmail = options.fromEmail;
    }

    if (options.hasAttachment) {
      params.hasAttachment = true;
    }

    if (options.since) {
      params.receivedTime = options.since;
    }

    try {
      const data = await this.makeRequest('/messages', params);
      
      logProcessing('emails_fetched', {
        count: data.data ? data.data.length : 0,
        params
      });

      return data.data || [];
    } catch (error) {
      logError(error, { context: 'getEmails', params });
      throw error;
    }
  }

  /**
   * Obtém detalhes de um e-mail específico
   */
  async getEmailDetails(messageId) {
    try {
      const data = await this.makeRequest(`/messages/${messageId}`);
      
      logProcessing('email_details_fetched', {
        messageId,
        subject: data.data?.subject,
        hasAttachments: data.data?.attachments?.length > 0
      });

      return data.data;
    } catch (error) {
      logError(error, { context: 'getEmailDetails', messageId });
      throw error;
    }
  }

  /**
   * Baixa um anexo específico
   */
  async downloadAttachment(messageId, attachmentId) {
    await this.ensureValidToken();

    try {
      const response = await axios.get(
        `${this.baseURL}/messages/${messageId}/attachments/${attachmentId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`
          },
          responseType: 'arraybuffer'
        }
      );

      logProcessing('attachment_downloaded', {
        messageId,
        attachmentId,
        size: response.data.byteLength
      });

      return response.data;
    } catch (error) {
      logError(error, { context: 'downloadAttachment', messageId, attachmentId });
      throw error;
    }
  }

  /**
   * Busca e-mails que correspondem aos critérios de filtro
   */
  async findMatchingEmails() {
    try {
      const emails = await this.getEmails({
        fromEmail: config.emailFilters.senderEmail,
        hasAttachment: true,
        limit: 100
      });

      const matchingEmails = [];

      for (const email of emails) {
        const emailDetails = await this.getEmailDetails(email.messageId);
        
        if (this.emailMatchesCriteria(emailDetails)) {
          matchingEmails.push(emailDetails);
        }
      }

      logProcessing('matching_emails_found', {
        totalEmails: emails.length,
        matchingEmails: matchingEmails.length
      });

      return matchingEmails;
    } catch (error) {
      logError(error, { context: 'findMatchingEmails' });
      throw error;
    }
  }

  /**
   * Verifica se um e-mail corresponde aos critérios de filtro
   */
  emailMatchesCriteria(emailDetails) {
    // Verifica remetente
    if (emailDetails.fromAddress !== config.emailFilters.senderEmail) {
      return false;
    }

    // Verifica se tem anexos
    if (!emailDetails.attachments || emailDetails.attachments.length === 0) {
      return false;
    }

    // Verifica se algum anexo contém as palavras-chave
    const hasMatchingAttachment = emailDetails.attachments.some(attachment => {
      const fileName = attachment.attachmentName.toLowerCase();
      return config.emailFilters.attachmentKeywords.some(keyword => 
        fileName.includes(keyword.toLowerCase())
      );
    });

    if (!hasMatchingAttachment) {
      return false;
    }

    // Verifica tipo de arquivo
    const hasSupportedFileType = emailDetails.attachments.some(attachment => {
      const fileName = attachment.attachmentName.toLowerCase();
      return config.emailFilters.supportedFileTypes.some(type => 
        fileName.endsWith(`.${type}`)
      );
    });

    return hasSupportedFileType;
  }

  /**
   * Obtém anexos relevantes de um e-mail
   */
  getRelevantAttachments(emailDetails) {
    return emailDetails.attachments.filter(attachment => {
      const fileName = attachment.attachmentName.toLowerCase();
      
      // Verifica palavra-chave
      const hasKeyword = config.emailFilters.attachmentKeywords.some(keyword => 
        fileName.includes(keyword.toLowerCase())
      );

      // Verifica tipo de arquivo
      const hasSupportedType = config.emailFilters.supportedFileTypes.some(type => 
        fileName.endsWith(`.${type}`)
      );

      return hasKeyword && hasSupportedType;
    });
  }
}

module.exports = ZohoMailClient;
