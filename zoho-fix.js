#!/usr/bin/env node

/**
 * Versão corrigida e simplificada para obter token do Zoho
 */

const https = require('https');
const querystring = require('querystring');
const readline = require('readline');
const fs = require('fs');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise(resolve => rl.question(prompt, resolve));
}

async function getZohoTokenFixed() {
  console.log('🔧 Zoho Token - Versão Corrigida');
  console.log('=================================');
  console.log('');
  
  console.log('📋 IMPORTANTE: Siga estes passos EXATAMENTE:');
  console.log('');
  console.log('1️⃣ Vá para: https://api-console.zoho.com/');
  console.log('2️⃣ Clique em "Add Client" → "Server-based Applications"');
  console.log('3️⃣ Preencha:');
  console.log('   - Client Name: Invoice Automation');
  console.log('   - Homepage URL: http://localhost:3001');
  console.log('   - Redirect URI: http://localhost:3001/callback');
  console.log('4️⃣ Anote o Client ID e Client Secret');
  console.log('');
  
  try {
    // Obter credenciais
    const clientId = await question('📝 Cole seu Client ID: ');
    const clientSecret = await question('📝 Cole seu Client Secret: ');
    
    if (!clientId || !clientSecret) {
      console.log('❌ Client ID e Client Secret são obrigatórios!');
      process.exit(1);
    }
    
    // Gerar URL de autorização com parâmetros corretos
    console.log('');
    console.log('5️⃣ AUTORIZAÇÃO:');
    console.log('================');
    
    const authUrl = `https://accounts.zoho.com/oauth/v2/auth?scope=ZohoMail.messages.READ,ZohoMail.folders.READ&client_id=${clientId}&response_type=code&redirect_uri=http://localhost:3001/callback&access_type=offline&prompt=consent`;
    
    console.log('');
    console.log('🔗 ACESSE ESTA URL (copie e cole no navegador):');
    console.log('');
    console.log(authUrl);
    console.log('');
    console.log('📋 INSTRUÇÕES:');
    console.log('- Use uma aba PRIVADA/INCÓGNITA');
    console.log('- Faça login na sua conta Zoho Mail');
    console.log('- Autorize o aplicativo');
    console.log('- Você será redirecionado para uma página de erro (NORMAL!)');
    console.log('- Na URL da página de erro, procure por "code="');
    console.log('- Copie APENAS o código (depois de "code=" e antes de "&")');
    console.log('');
    
    const authCode = await question('📝 Cole APENAS o código de autorização: ');
    
    if (!authCode) {
      console.log('❌ Código de autorização é obrigatório!');
      process.exit(1);
    }
    
    // Limpar o código (remover espaços e caracteres extras)
    const cleanCode = authCode.trim().split('&')[0];
    
    console.log('');
    console.log('⏳ Obtendo refresh token...');
    console.log('');
    
    // Fazer requisição para obter token
    const postData = querystring.stringify({
      grant_type: 'authorization_code',
      client_id: clientId.trim(),
      client_secret: clientSecret.trim(),
      redirect_uri: 'http://localhost:3001/callback',
      code: cleanCode
    });
    
    const options = {
      hostname: 'accounts.zoho.com',
      path: '/oauth/v2/token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': postData.length,
        'User-Agent': 'Invoice-Automation/1.0'
      }
    };
    
    const result = await new Promise((resolve, reject) => {
      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(data));
          } catch (e) {
            resolve({ error: 'parse_error', raw: data });
          }
        });
      });
      
      req.on('error', reject);
      req.write(postData);
      req.end();
    });
    
    console.log('📥 Resposta do Zoho:');
    console.log(JSON.stringify(result, null, 2));
    console.log('');
    
    if (result.refresh_token) {
      console.log('🎉 SUCESSO! Refresh token obtido!');
      console.log('');
      console.log('📋 SUAS CREDENCIAIS:');
      console.log('====================');
      console.log(`ZOHO_CLIENT_ID=${clientId}`);
      console.log(`ZOHO_CLIENT_SECRET=${clientSecret}`);
      console.log(`ZOHO_REFRESH_TOKEN=${result.refresh_token}`);
      console.log('');
      
      // Salvar no .env
      try {
        let envContent = '';
        if (fs.existsSync('.env')) {
          envContent = fs.readFileSync('.env', 'utf8');
        }
        
        // Atualizar credenciais
        const updates = {
          ZOHO_CLIENT_ID: clientId,
          ZOHO_CLIENT_SECRET: clientSecret,
          ZOHO_REFRESH_TOKEN: result.refresh_token
        };
        
        for (const [key, value] of Object.entries(updates)) {
          const regex = new RegExp(`^${key}=.*$`, 'm');
          if (envContent.match(regex)) {
            envContent = envContent.replace(regex, `${key}=${value}`);
          } else {
            envContent += `\n${key}=${value}`;
          }
        }
        
        fs.writeFileSync('.env', envContent);
        console.log('✅ Credenciais salvas no arquivo .env!');
        
      } catch (error) {
        console.log('⚠️  Erro ao salvar .env:', error.message);
        console.log('📋 Copie as credenciais acima manualmente');
      }
      
      console.log('');
      console.log('🚀 PRÓXIMOS PASSOS:');
      console.log('===================');
      console.log('1. Execute: npm start');
      console.log('2. Acesse: http://localhost:3000');
      console.log('3. Teste o sistema!');
      
    } else if (result.error) {
      console.log('❌ ERRO:', result.error);
      
      if (result.error_description) {
        console.log('📝 Descrição:', result.error_description);
      }
      
      console.log('');
      console.log('🔧 SOLUÇÕES COMUNS:');
      console.log('===================');
      
      switch (result.error) {
        case 'invalid_code':
          console.log('- O código expirou (códigos duram apenas alguns minutos)');
          console.log('- Gere um novo código usando a URL acima');
          console.log('- Use o código IMEDIATAMENTE após receber');
          break;
          
        case 'invalid_client':
          console.log('- Client ID ou Client Secret incorretos');
          console.log('- Verifique no Zoho API Console');
          break;
          
        case 'invalid_grant':
          console.log('- Código já foi usado ou expirou');
          console.log('- Redirect URI não confere exatamente');
          console.log('- Gere um novo código');
          break;
          
        default:
          console.log('- Tente gerar um novo código de autorização');
          console.log('- Verifique se está usando a conta Zoho correta');
          console.log('- Use uma aba privada/incógnita');
      }
      
      console.log('');
      console.log('🔄 Execute este script novamente para tentar outra vez');
      
    } else {
      console.log('❌ Resposta inesperada do Zoho');
      console.log('📋 Tente novamente ou use uma conta Zoho diferente');
    }
    
  } catch (error) {
    console.log('❌ Erro:', error.message);
    console.log('');
    console.log('🆘 AJUDA:');
    console.log('- Verifique sua conexão com a internet');
    console.log('- Tente usar uma VPN se estiver bloqueado');
    console.log('- Execute: node debug-zoho.js para mais detalhes');
  } finally {
    rl.close();
  }
}

if (require.main === module) {
  getZohoTokenFixed();
}
