const EmailNotifier = require('./emailNotifier');
const SlackNotifier = require('./slackNotifier');
const config = require('../config/config');
const { logger, logError, logProcessing } = require('../config/logger');

/**
 * Serviço central de notificações que coordena e-mail e Slack
 */
class NotificationService {
  constructor() {
    this.emailNotifier = new EmailNotifier();
    this.slackNotifier = new SlackNotifier();
  }

  /**
   * Envia notificação de conversão de invoice
   */
  async sendConversionNotification(conversionResults) {
    const notifications = [];

    try {
      logProcessing('conversion_notification_started', {
        invoiceNumber: conversionResults.invoiceData.invoiceNumber,
        emailEnabled: config.notifications.email.enabled,
        slackEnabled: config.notifications.slack.enabled
      });

      // Envia notificação por e-mail
      if (config.notifications.email.enabled) {
        try {
          const emailResult = await this.emailNotifier.sendInvoiceProcessedNotification(
            conversionResults.invoiceData,
            conversionResults
          );
          notifications.push({ type: 'email', success: true, result: emailResult });
        } catch (error) {
          logError(error, { context: 'email notification', invoiceNumber: conversionResults.invoiceData.invoiceNumber });
          notifications.push({ type: 'email', success: false, error: error.message });
        }
      }

      // Envia notificação via Slack
      if (config.notifications.slack.enabled) {
        try {
          const slackResult = await this.slackNotifier.sendInvoiceProcessedNotification(
            conversionResults.invoiceData,
            conversionResults
          );
          notifications.push({ type: 'slack', success: true, result: slackResult });
        } catch (error) {
          logError(error, { context: 'slack notification', invoiceNumber: conversionResults.invoiceData.invoiceNumber });
          notifications.push({ type: 'slack', success: false, error: error.message });
        }
      }

      logProcessing('conversion_notification_completed', {
        invoiceNumber: conversionResults.invoiceData.invoiceNumber,
        notifications: notifications.map(n => ({ type: n.type, success: n.success }))
      });

      return notifications;
    } catch (error) {
      logError(error, { context: 'sendConversionNotification', conversionResults });
      throw error;
    }
  }

  /**
   * Envia notificação de erro
   */
  async sendErrorNotification(error, context = {}) {
    const notifications = [];

    try {
      logProcessing('error_notification_started', {
        error: error.message,
        context,
        emailEnabled: config.notifications.email.enabled,
        slackEnabled: config.notifications.slack.enabled
      });

      // Envia notificação por e-mail
      if (config.notifications.email.enabled) {
        try {
          const emailResult = await this.emailNotifier.sendErrorNotification(error, context);
          notifications.push({ type: 'email', success: true, result: emailResult });
        } catch (emailError) {
          logError(emailError, { context: 'error email notification', originalError: error.message });
          notifications.push({ type: 'email', success: false, error: emailError.message });
        }
      }

      // Envia notificação via Slack
      if (config.notifications.slack.enabled) {
        try {
          const slackResult = await this.slackNotifier.sendErrorNotification(error, context);
          notifications.push({ type: 'slack', success: true, result: slackResult });
        } catch (slackError) {
          logError(slackError, { context: 'error slack notification', originalError: error.message });
          notifications.push({ type: 'slack', success: false, error: slackError.message });
        }
      }

      logProcessing('error_notification_completed', {
        error: error.message,
        notifications: notifications.map(n => ({ type: n.type, success: n.success }))
      });

      return notifications;
    } catch (notificationError) {
      logError(notificationError, { context: 'sendErrorNotification', originalError: error.message });
      throw notificationError;
    }
  }

  /**
   * Envia relatório diário
   */
  async sendDailyReport(reportData) {
    const notifications = [];

    try {
      logProcessing('daily_report_notification_started', {
        reportData: {
          processedInvoices: reportData.processedInvoices,
          errors: reportData.errors,
          totalFiles: reportData.totalFiles
        },
        emailEnabled: config.notifications.email.enabled,
        slackEnabled: config.notifications.slack.enabled
      });

      // Envia relatório por e-mail
      if (config.notifications.email.enabled) {
        try {
          const emailResult = await this.emailNotifier.sendDailyReport(reportData);
          notifications.push({ type: 'email', success: true, result: emailResult });
        } catch (error) {
          logError(error, { context: 'daily report email notification' });
          notifications.push({ type: 'email', success: false, error: error.message });
        }
      }

      // Envia relatório via Slack
      if (config.notifications.slack.enabled) {
        try {
          const slackResult = await this.slackNotifier.sendDailyReport(reportData);
          notifications.push({ type: 'slack', success: true, result: slackResult });
        } catch (error) {
          logError(error, { context: 'daily report slack notification' });
          notifications.push({ type: 'slack', success: false, error: error.message });
        }
      }

      logProcessing('daily_report_notification_completed', {
        notifications: notifications.map(n => ({ type: n.type, success: n.success }))
      });

      return notifications;
    } catch (error) {
      logError(error, { context: 'sendDailyReport', reportData });
      throw error;
    }
  }

  /**
   * Envia notificação de sistema iniciado
   */
  async sendSystemStartedNotification() {
    const notifications = [];

    try {
      const message = {
        subject: 'Sistema de Automação Iniciado',
        text: `O Sistema de Automação de Invoices foi iniciado com sucesso em ${new Date().toLocaleString('pt-BR')}.`,
        data: {
          timestamp: new Date().toISOString(),
          version: config.app.version,
          environment: config.app.env
        }
      };

      // Notificação por e-mail
      if (config.notifications.email.enabled) {
        try {
          // Implementar envio de e-mail de sistema iniciado se necessário
          notifications.push({ type: 'email', success: true, message: 'System started notification would be sent' });
        } catch (error) {
          notifications.push({ type: 'email', success: false, error: error.message });
        }
      }

      // Notificação via Slack
      if (config.notifications.slack.enabled) {
        try {
          const slackMessage = {
            text: '🚀 Sistema de Automação Iniciado',
            attachments: [
              {
                color: 'good',
                title: 'Sistema Online',
                fields: [
                  {
                    title: 'Versão',
                    value: config.app.version,
                    short: true
                  },
                  {
                    title: 'Ambiente',
                    value: config.app.env,
                    short: true
                  },
                  {
                    title: 'Data/Hora',
                    value: new Date().toLocaleString('pt-BR'),
                    short: false
                  }
                ],
                footer: 'Sistema de Automação de Invoices',
                ts: Math.floor(Date.now() / 1000)
              }
            ]
          };

          const slackResult = await this.slackNotifier.sendSlackMessage(slackMessage);
          notifications.push({ type: 'slack', success: true, result: slackResult });
        } catch (error) {
          notifications.push({ type: 'slack', success: false, error: error.message });
        }
      }

      logProcessing('system_started_notification_sent', {
        notifications: notifications.map(n => ({ type: n.type, success: n.success }))
      });

      return notifications;
    } catch (error) {
      logError(error, { context: 'sendSystemStartedNotification' });
      throw error;
    }
  }

  /**
   * Testa todas as configurações de notificação
   */
  async testAllNotifications() {
    const results = {
      email: null,
      slack: null,
      summary: {
        totalTests: 0,
        successfulTests: 0,
        failedTests: 0
      }
    };

    // Testa e-mail
    if (config.notifications.email.enabled) {
      results.summary.totalTests++;
      try {
        results.email = await this.emailNotifier.testEmailConfiguration();
        results.summary.successfulTests++;
        logger.info('Email notification test passed');
      } catch (error) {
        results.email = { error: error.message };
        results.summary.failedTests++;
        logError(error, { context: 'email notification test' });
      }
    }

    // Testa Slack
    if (config.notifications.slack.enabled) {
      results.summary.totalTests++;
      try {
        results.slack = await this.slackNotifier.testSlackConfiguration();
        results.summary.successfulTests++;
        logger.info('Slack notification test passed');
      } catch (error) {
        results.slack = { error: error.message };
        results.summary.failedTests++;
        logError(error, { context: 'slack notification test' });
      }
    }

    logProcessing('notification_tests_completed', results.summary);

    return results;
  }

  /**
   * Obtém estatísticas de notificações
   */
  getNotificationStats() {
    return {
      email: {
        enabled: config.notifications.email.enabled,
        configured: this.emailNotifier.isConfigured,
        recipients: config.notifications.email.recipients.length
      },
      slack: {
        enabled: config.notifications.slack.enabled,
        configured: this.slackNotifier.isConfigured,
        webhookConfigured: !!config.notifications.slack.webhookUrl
      }
    };
  }

  /**
   * Gera relatório de atividades do dia
   */
  async generateDailyActivityReport() {
    try {
      // TODO: Implementar coleta de estatísticas dos logs
      const reportData = {
        date: new Date().toLocaleDateString('pt-BR'),
        processedInvoices: 0, // Buscar dos logs
        totalFiles: 0, // Buscar dos logs
        errors: 0, // Buscar dos logs
        successRate: '0%',
        recentInvoices: [] // Buscar dos logs
      };

      // Calcular taxa de sucesso
      if (reportData.processedInvoices > 0) {
        const successfulInvoices = reportData.processedInvoices - reportData.errors;
        reportData.successRate = `${Math.round((successfulInvoices / reportData.processedInvoices) * 100)}%`;
      }

      return reportData;
    } catch (error) {
      logError(error, { context: 'generateDailyActivityReport' });
      throw error;
    }
  }
}

module.exports = NotificationService;
