#!/usr/bin/env node

/**
 * Assistente para configurar credenciais do Zoho Mail
 */

const fs = require('fs');
const path = require('path');
const http = require('http');
const https = require('https');
const querystring = require('querystring');

console.log('🔑 Assistente de Configuração - Zoho Mail API');
console.log('==============================================');

// Função para fazer requisição HTTPS
function makeHttpsRequest(options, postData) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          resolve(data);
        }
      });
    });

    req.on('error', reject);
    if (postData) req.write(postData);
    req.end();
  });
}

// Servidor para ajudar com OAuth
const server = http.createServer(async (req, res) => {
  const url = new URL(req.url, `http://${req.headers.host}`);

  // Configurar CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Rota para obter token
  if (url.pathname === '/get-token' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', async () => {
      try {
        const data = JSON.parse(body);

        const postData = querystring.stringify({
          grant_type: data.grant_type,
          client_id: data.client_id,
          client_secret: data.client_secret,
          redirect_uri: data.redirect_uri,
          code: data.code
        });

        const options = {
          hostname: 'accounts.zoho.com',
          path: '/oauth/v2/token',
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Content-Length': postData.length
          }
        };

        const result = await makeHttpsRequest(options, postData);

        res.writeHead(200, { 'Content-Type': 'application/json' });
        if (result.refresh_token) {
          res.end(JSON.stringify({ success: true, ...result }));
        } else {
          res.end(JSON.stringify({ success: false, error: result.error || 'Token não recebido', details: result }));
        }
      } catch (error) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: error.message }));
      }
    });
    return;
  }

  // Rota para salvar .env
  if (url.pathname === '/save-env' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', async () => {
      try {
        const credentials = JSON.parse(body);

        // Ler .env atual
        let envContent = '';
        if (fs.existsSync('.env')) {
          envContent = fs.readFileSync('.env', 'utf8');
        }

        // Atualizar ou adicionar credenciais
        const updates = {
          ZOHO_CLIENT_ID: credentials.ZOHO_CLIENT_ID,
          ZOHO_CLIENT_SECRET: credentials.ZOHO_CLIENT_SECRET,
          ZOHO_REFRESH_TOKEN: credentials.ZOHO_REFRESH_TOKEN
        };

        for (const [key, value] of Object.entries(updates)) {
          const regex = new RegExp(`^${key}=.*$`, 'm');
          if (envContent.match(regex)) {
            envContent = envContent.replace(regex, `${key}=${value}`);
          } else {
            envContent += `\n${key}=${value}`;
          }
        }

        fs.writeFileSync('.env', envContent);

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: true }));
      } catch (error) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: error.message }));
      }
    });
    return;
  }

  res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });

  if (url.pathname === '/callback') {
    // Página de callback do OAuth
    const code = url.searchParams.get('code');
    const error = url.searchParams.get('error');
    
    if (error) {
      res.end(`
        <h1>❌ Erro na Autorização</h1>
        <p>Erro: ${error}</p>
        <p><a href="/">← Voltar</a></p>
      `);
      return;
    }
    
    if (code) {
      res.end(`
        <h1>✅ Código de Autorização Recebido!</h1>
        <p><strong>Seu código:</strong></p>
        <pre style="background: #f0f0f0; padding: 10px; border-radius: 5px;">${code}</pre>
        <p><strong>Próximo passo:</strong> Use este código para obter o refresh token</p>
        <button onclick="copyCode()">📋 Copiar Código</button>
        <script>
          function copyCode() {
            navigator.clipboard.writeText('${code}');
            alert('Código copiado!');
          }
        </script>
        <p><a href="/">← Voltar ao início</a></p>
      `);
      return;
    }
  }
  
  // Página principal
  const html = `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuração Zoho Mail API</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 900px; 
            margin: 0 auto; 
            padding: 20px;
            background: #f5f5f5;
        }
        .container { 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step { 
            padding: 20px; 
            margin: 15px 0; 
            border-radius: 8px; 
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .step h3 { margin-top: 0; color: #007bff; }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto;
            border: 1px solid #dee2e6;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .code-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 Configuração Zoho Mail API</h1>
        
        <div class="success">
            <strong>✅ Servidor de configuração ativo!</strong><br>
            Este assistente vai te ajudar a obter as credenciais do Zoho Mail.
        </div>
        
        <div class="step">
            <h3>📋 Passo 1: Criar Aplicação no Zoho</h3>
            <p>1. Acesse o <strong>Zoho API Console</strong>:</p>
            <a href="https://api-console.zoho.com/" target="_blank" class="btn">🌐 Abrir Zoho API Console</a>
            
            <p>2. Clique em <strong>"Add Client"</strong></p>
            <p>3. Escolha <strong>"Server-based Applications"</strong></p>
            <p>4. Preencha os dados:</p>
            <ul>
                <li><strong>Client Name:</strong> Invoice Automation</li>
                <li><strong>Homepage URL:</strong> http://localhost:3001</li>
                <li><strong>Authorized Redirect URIs:</strong> http://localhost:3001/callback</li>
            </ul>
            <p>5. Anote o <strong>Client ID</strong> e <strong>Client Secret</strong></p>
        </div>
        
        <div class="step">
            <h3>🔐 Passo 2: Gerar URL de Autorização</h3>
            <p>Insira seu <strong>Client ID</strong> aqui:</p>
            <input type="text" id="clientId" placeholder="Seu Client ID do Zoho" class="code-input">
            <br><br>
            <button onclick="generateAuthUrl()" class="btn btn-success">🔗 Gerar URL de Autorização</button>
            
            <div id="authUrlResult" style="display: none; margin-top: 15px;">
                <p><strong>URL de Autorização:</strong></p>
                <pre id="authUrl"></pre>
                <a id="authLink" href="#" target="_blank" class="btn">🚀 Autorizar Aplicação</a>
            </div>
        </div>
        
        <div class="step">
            <h3>🎫 Passo 3: Obter Refresh Token</h3>
            <p>Após autorizar, você receberá um <strong>código</strong>. Cole-o aqui:</p>
            <input type="text" id="authCode" placeholder="Código de autorização" class="code-input">
            <br><br>
            <p>Insira também seu <strong>Client Secret</strong>:</p>
            <input type="text" id="clientSecret" placeholder="Seu Client Secret" class="code-input">
            <br><br>
            <button onclick="getRefreshToken()" class="btn btn-success">🔄 Obter Refresh Token</button>
            
            <div id="tokenResult" style="display: none; margin-top: 15px;">
                <div class="success">
                    <h4>✅ Credenciais Obtidas!</h4>
                    <p><strong>Refresh Token:</strong></p>
                    <pre id="refreshToken"></pre>
                    <button onclick="copyCredentials()" class="btn">📋 Copiar Todas as Credenciais</button>
                </div>
            </div>
        </div>
        
        <div class="step">
            <h3>⚙️ Passo 4: Configurar .env</h3>
            <p>Adicione estas linhas ao seu arquivo <code>.env</code>:</p>
            <pre id="envConfig">
# Configure primeiro os passos acima
ZOHO_CLIENT_ID=seu_client_id
ZOHO_CLIENT_SECRET=seu_client_secret  
ZOHO_REFRESH_TOKEN=seu_refresh_token
            </pre>
        </div>
        
        <div class="warning">
            <strong>⚠️ Importante:</strong>
            <ul>
                <li>Mantenha suas credenciais seguras</li>
                <li>Não compartilhe o Client Secret ou Refresh Token</li>
                <li>O Refresh Token não expira, mas pode ser revogado</li>
            </ul>
        </div>
    </div>
    
    <script>
        function generateAuthUrl() {
            const clientId = document.getElementById('clientId').value;
            if (!clientId) {
                alert('Por favor, insira o Client ID');
                return;
            }
            
            const authUrl = \`https://accounts.zoho.com/oauth/v2/auth?scope=ZohoMail.messages.READ&client_id=\${clientId}&response_type=code&redirect_uri=http://localhost:3001/callback&access_type=offline\`;
            
            document.getElementById('authUrl').textContent = authUrl;
            document.getElementById('authLink').href = authUrl;
            document.getElementById('authUrlResult').style.display = 'block';
        }
        
        async function getRefreshToken() {
            const clientId = document.getElementById('clientId').value;
            const clientSecret = document.getElementById('clientSecret').value;
            const authCode = document.getElementById('authCode').value;

            if (!clientId || !clientSecret || !authCode) {
                alert('Por favor, preencha todos os campos');
                return;
            }

            // Mostrar loading
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '⏳ Obtendo token...';
            button.disabled = true;

            try {
                // Usar nosso servidor como proxy para evitar CORS
                const response = await fetch('/get-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        grant_type: 'authorization_code',
                        client_id: clientId,
                        client_secret: clientSecret,
                        redirect_uri: 'http://localhost:3001/callback',
                        code: authCode
                    })
                });

                const data = await response.json();

                if (data.success && data.refresh_token) {
                    document.getElementById('refreshToken').textContent = data.refresh_token;
                    document.getElementById('tokenResult').style.display = 'block';

                    // Atualizar configuração .env
                    const envConfig = \`ZOHO_CLIENT_ID=\${clientId}
ZOHO_CLIENT_SECRET=\${clientSecret}
ZOHO_REFRESH_TOKEN=\${data.refresh_token}\`;
                    document.getElementById('envConfig').textContent = envConfig;

                    // Salvar automaticamente no .env
                    await fetch('/save-env', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            ZOHO_CLIENT_ID: clientId,
                            ZOHO_CLIENT_SECRET: clientSecret,
                            ZOHO_REFRESH_TOKEN: data.refresh_token
                        })
                    });

                    alert('✅ Credenciais salvas no arquivo .env!');
                } else {
                    alert('❌ Erro ao obter refresh token: ' + (data.error || JSON.stringify(data)));
                }
            } catch (error) {
                alert('❌ Erro de conexão: ' + error.message);
                console.error('Erro completo:', error);
            } finally {
                button.textContent = originalText;
                button.disabled = false;
            }
        }
        
        function copyCredentials() {
            const clientId = document.getElementById('clientId').value;
            const clientSecret = document.getElementById('clientSecret').value;
            const refreshToken = document.getElementById('refreshToken').textContent;
            
            const credentials = \`ZOHO_CLIENT_ID=\${clientId}
ZOHO_CLIENT_SECRET=\${clientSecret}
ZOHO_REFRESH_TOKEN=\${refreshToken}\`;
            
            navigator.clipboard.writeText(credentials).then(() => {
                alert('Credenciais copiadas! Cole no seu arquivo .env');
            });
        }
    </script>
</body>
</html>`;
  
  res.end(html);
});

const PORT = 3001;

server.listen(PORT, () => {
  console.log('');
  console.log('🌐 Assistente de configuração iniciado!');
  console.log(`📍 Acesse: http://localhost:${PORT}`);
  console.log('');
  console.log('📋 Este assistente vai te ajudar a:');
  console.log('   1. Criar aplicação no Zoho API Console');
  console.log('   2. Gerar URL de autorização');
  console.log('   3. Obter refresh token');
  console.log('   4. Configurar arquivo .env');
  console.log('');
  console.log('💡 Para parar, pressione Ctrl+C');
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Parando assistente...');
  server.close(() => {
    console.log('✅ Assistente parado!');
    process.exit(0);
  });
});
