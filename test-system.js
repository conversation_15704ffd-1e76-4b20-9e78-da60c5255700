#!/usr/bin/env node

/**
 * Script para testar o sistema com as dependências instaladas
 */

const fs = require('fs-extra');
const path = require('path');

console.log('🧪 Testando Sistema de Automação de Invoices');
console.log('============================================');

async function testSystem() {
  try {
    // Verificar se as dependências estão instaladas
    console.log('📦 Verificando dependências...');
    
    const dependencies = ['express', 'cors', 'dotenv', 'fs-extra'];
    const missing = [];
    
    for (const dep of dependencies) {
      try {
        require(dep);
        console.log(`✅ ${dep} - OK`);
      } catch (error) {
        console.log(`❌ ${dep} - FALTANDO`);
        missing.push(dep);
      }
    }
    
    if (missing.length > 0) {
      console.log(`\n⚠️  Dependências faltando: ${missing.join(', ')}`);
      console.log('Execute: npm install');
      return;
    }
    
    // Verificar estrutura de diretórios
    console.log('\n📁 Verificando estrutura de diretórios...');
    const requiredDirs = [
      './data',
      './data/input',
      './data/output',
      './data/logs',
      './src',
      './web'
    ];
    
    for (const dir of requiredDirs) {
      if (await fs.pathExists(dir)) {
        console.log(`✅ ${dir} - OK`);
      } else {
        console.log(`❌ ${dir} - FALTANDO`);
        await fs.ensureDir(dir);
        console.log(`✅ ${dir} - CRIADO`);
      }
    }
    
    // Verificar arquivo .env
    console.log('\n⚙️  Verificando configuração...');
    if (await fs.pathExists('.env')) {
      console.log('✅ Arquivo .env - OK');
      
      // Ler e verificar configurações importantes
      const envContent = await fs.readFile('.env', 'utf8');
      const requiredVars = [
        'ZOHO_CLIENT_ID',
        'ZOHO_CLIENT_SECRET', 
        'ZOHO_REFRESH_TOKEN',
        'SENDER_EMAIL',
        'ATTACHMENT_KEYWORD'
      ];
      
      console.log('\n🔍 Verificando variáveis de ambiente:');
      for (const varName of requiredVars) {
        if (envContent.includes(`${varName}=`) && !envContent.includes(`${varName}=your_`)) {
          console.log(`✅ ${varName} - CONFIGURADO`);
        } else {
          console.log(`⚠️  ${varName} - PRECISA CONFIGURAR`);
        }
      }
    } else {
      console.log('❌ Arquivo .env - FALTANDO');
      console.log('📋 Copiando .env.example...');
      if (await fs.pathExists('.env.example')) {
        await fs.copy('.env.example', '.env');
        console.log('✅ Arquivo .env criado');
      }
    }
    
    // Testar servidor web básico
    console.log('\n🌐 Testando servidor web...');
    try {
      const express = require('express');
      const app = express();
      
      app.get('/test', (req, res) => {
        res.json({ 
          status: 'OK', 
          message: 'Sistema funcionando!',
          timestamp: new Date().toISOString()
        });
      });
      
      const server = app.listen(3002, () => {
        console.log('✅ Servidor web - OK (porta 3002)');
        server.close();
      });
      
    } catch (error) {
      console.log('❌ Servidor web - ERRO:', error.message);
    }
    
    // Verificar arquivos principais
    console.log('\n📄 Verificando arquivos principais...');
    const mainFiles = [
      'src/index.js',
      'web/server.js',
      'package.json'
    ];
    
    for (const file of mainFiles) {
      if (await fs.pathExists(file)) {
        console.log(`✅ ${file} - OK`);
      } else {
        console.log(`❌ ${file} - FALTANDO`);
      }
    }
    
    // Resumo
    console.log('\n📊 RESUMO DO TESTE');
    console.log('==================');
    console.log('✅ Dependências básicas instaladas');
    console.log('✅ Estrutura de diretórios OK');
    console.log('✅ Sistema básico funcionando');
    console.log('');
    console.log('🎯 PRÓXIMOS PASSOS:');
    console.log('1. Configure as credenciais do Zoho Mail no .env');
    console.log('2. Use o assistente em http://localhost:3001');
    console.log('3. Teste o sistema completo com: npm start');
    console.log('');
    console.log('🆘 PRECISA DE AJUDA?');
    console.log('- Configurar Zoho: http://localhost:3001');
    console.log('- Sistema básico: node start-simple.js');
    console.log('- Testar novamente: node test-system.js');
    
  } catch (error) {
    console.error('❌ Erro durante o teste:', error.message);
  }
}

testSystem();
