#!/usr/bin/env node

const config = require('./config/config');
const { logger, logError, logProcessing } = require('./config/logger');

// Importa serviços principais
const EmailMonitor = require('./email/emailMonitor');
const QuickBooksConverter = require('./converters/quickbooksConverter');
const NotificationService = require('./notifications/notificationService');
const StorageManager = require('./storage/storageManager');
const WebServer = require('../web/server');

/**
 * Classe principal do Sistema de Automação de Invoices
 */
class InvoiceAutomationSystem {
  constructor() {
    this.services = {
      emailMonitor: null,
      quickbooksConverter: null,
      notificationService: null,
      storageManager: null,
      webServer: null
    };
    
    this.isRunning = false;
    this.startTime = null;
    
    // Configura handlers de processo
    this.setupProcessHandlers();
  }

  /**
   * Inicializa todos os serviços do sistema
   */
  async initialize() {
    try {
      logger.info('Inicializando Sistema de Automação de Invoices...');
      logger.info(`Versão: ${config.app.version}`);
      logger.info(`Ambiente: ${config.app.env}`);
      
      this.startTime = new Date();

      // Inicializa serviços
      await this.initializeServices();
      
      // Valida configuração
      await this.validateConfiguration();
      
      logger.info('Sistema inicializado com sucesso');
      
      return true;
    } catch (error) {
      logError(error, { context: 'system initialization' });
      throw error;
    }
  }

  /**
   * Inicializa todos os serviços
   */
  async initializeServices() {
    try {
      // Storage Manager
      this.services.storageManager = new StorageManager();
      logger.info('✓ Storage Manager inicializado');

      // Notification Service
      this.services.notificationService = new NotificationService();
      logger.info('✓ Notification Service inicializado');

      // QuickBooks Converter
      this.services.quickbooksConverter = new QuickBooksConverter();
      logger.info('✓ QuickBooks Converter inicializado');

      // Email Monitor
      this.services.emailMonitor = new EmailMonitor();
      logger.info('✓ Email Monitor inicializado');

      // Web Server (se habilitado)
      if (config.web.enabled) {
        this.services.webServer = new WebServer();
        logger.info('✓ Web Server inicializado');
      }

    } catch (error) {
      logError(error, { context: 'service initialization' });
      throw error;
    }
  }

  /**
   * Valida configuração do sistema
   */
  async validateConfiguration() {
    const issues = [];

    // Verifica configuração do Zoho Mail
    if (!config.zohoMail.clientId || !config.zohoMail.clientSecret) {
      issues.push('Credenciais do Zoho Mail não configuradas');
    }

    // Verifica configuração de notificações
    if (config.notifications.email.enabled && !config.notifications.email.smtp.auth.user) {
      issues.push('Configuração de e-mail para notificações incompleta');
    }

    if (config.notifications.slack.enabled && !config.notifications.slack.webhookUrl) {
      issues.push('Webhook do Slack não configurado');
    }

    // Verifica diretórios
    const fs = require('fs-extra');
    const requiredDirs = [
      config.storage.inputPath,
      config.storage.outputPath,
      config.storage.logsPath
    ];

    for (const dir of requiredDirs) {
      if (!(await fs.pathExists(dir))) {
        issues.push(`Diretório não existe: ${dir}`);
      }
    }

    if (issues.length > 0) {
      logger.warn('Problemas de configuração encontrados:', issues);
      
      // Envia notificação sobre problemas de configuração se possível
      if (this.services.notificationService) {
        try {
          await this.services.notificationService.sendErrorNotification(
            new Error('Problemas de configuração detectados'),
            { issues }
          );
        } catch (notificationError) {
          logError(notificationError, { context: 'configuration validation notification' });
        }
      }
    }

    return issues;
  }

  /**
   * Inicia o sistema
   */
  async start() {
    try {
      if (this.isRunning) {
        logger.warn('Sistema já está em execução');
        return;
      }

      logger.info('Iniciando Sistema de Automação de Invoices...');

      // Inicializa se necessário
      if (!this.services.emailMonitor) {
        await this.initialize();
      }

      // Inicia serviços
      await this.startServices();

      this.isRunning = true;

      // Envia notificação de sistema iniciado
      if (this.services.notificationService) {
        try {
          await this.services.notificationService.sendSystemStartedNotification();
        } catch (error) {
          logError(error, { context: 'system started notification' });
        }
      }

      logger.info('Sistema de Automação de Invoices iniciado com sucesso');
      logger.info(`Interface web disponível em: http://localhost:${config.web.port}`);

      return true;
    } catch (error) {
      logError(error, { context: 'system start' });
      throw error;
    }
  }

  /**
   * Inicia todos os serviços
   */
  async startServices() {
    try {
      // Inicia Web Server se habilitado
      if (this.services.webServer && config.web.enabled) {
        await this.services.webServer.start();
        logger.info('✓ Web Server iniciado');
      }

      // Inicia Email Monitor se habilitado
      if (config.zohoMail.enabled && config.zohoMail.autoStart) {
        await this.services.emailMonitor.start();
        logger.info('✓ Email Monitor iniciado');
      }

    } catch (error) {
      logError(error, { context: 'start services' });
      throw error;
    }
  }

  /**
   * Para o sistema
   */
  async stop() {
    try {
      if (!this.isRunning) {
        logger.warn('Sistema não está em execução');
        return;
      }

      logger.info('Parando Sistema de Automação de Invoices...');

      // Para serviços
      await this.stopServices();

      this.isRunning = false;

      logger.info('Sistema parado com sucesso');

      return true;
    } catch (error) {
      logError(error, { context: 'system stop' });
      throw error;
    }
  }

  /**
   * Para todos os serviços
   */
  async stopServices() {
    try {
      // Para Email Monitor
      if (this.services.emailMonitor) {
        await this.services.emailMonitor.stop();
        logger.info('✓ Email Monitor parado');
      }

      // Para Web Server
      if (this.services.webServer) {
        await this.services.webServer.stop();
        logger.info('✓ Web Server parado');
      }

    } catch (error) {
      logError(error, { context: 'stop services' });
      throw error;
    }
  }

  /**
   * Reinicia o sistema
   */
  async restart() {
    try {
      logger.info('Reiniciando sistema...');
      
      await this.stop();
      await new Promise(resolve => setTimeout(resolve, 2000)); // Aguarda 2 segundos
      await this.start();
      
      logger.info('Sistema reiniciado com sucesso');
    } catch (error) {
      logError(error, { context: 'system restart' });
      throw error;
    }
  }

  /**
   * Obtém status do sistema
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      startTime: this.startTime,
      uptime: this.startTime ? Date.now() - this.startTime.getTime() : 0,
      version: config.app.version,
      environment: config.app.env,
      services: {
        emailMonitor: this.services.emailMonitor ? this.services.emailMonitor.isRunning() : false,
        webServer: this.services.webServer ? true : false,
        notificationService: this.services.notificationService ? true : false,
        storageManager: this.services.storageManager ? true : false,
        quickbooksConverter: this.services.quickbooksConverter ? true : false
      }
    };
  }

  /**
   * Processa um arquivo manualmente
   */
  async processFile(filePath, options = {}) {
    try {
      logProcessing('manual_file_processing_started', { filePath, options });

      // TODO: Implementar processamento manual completo
      // 1. Extrair dados do arquivo
      // 2. Converter para QuickBooks
      // 3. Salvar arquivos
      // 4. Enviar notificações

      logger.info('Processamento manual de arquivo não implementado completamente');
      
      return {
        success: false,
        message: 'Processamento manual não implementado ainda'
      };
    } catch (error) {
      logError(error, { context: 'manual file processing', filePath });
      throw error;
    }
  }

  /**
   * Configura handlers de processo para shutdown graceful
   */
  setupProcessHandlers() {
    // Shutdown graceful
    const gracefulShutdown = async (signal) => {
      logger.info(`Recebido sinal ${signal}, iniciando shutdown graceful...`);
      
      try {
        await this.stop();
        process.exit(0);
      } catch (error) {
        logError(error, { context: 'graceful shutdown' });
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handler de erros não capturados
    process.on('uncaughtException', (error) => {
      logError(error, { context: 'uncaught exception' });
      
      // Tenta enviar notificação de erro crítico
      if (this.services.notificationService) {
        this.services.notificationService.sendErrorNotification(error, {
          type: 'uncaught_exception',
          critical: true
        }).catch(() => {
          // Ignora erro na notificação
        });
      }
      
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      const error = new Error(`Unhandled Rejection: ${reason}`);
      logError(error, { context: 'unhandled rejection', promise });
      
      // Tenta enviar notificação de erro crítico
      if (this.services.notificationService) {
        this.services.notificationService.sendErrorNotification(error, {
          type: 'unhandled_rejection',
          critical: true,
          reason
        }).catch(() => {
          // Ignora erro na notificação
        });
      }
    });
  }
}

// Função principal para execução via CLI
async function main() {
  try {
    const system = new InvoiceAutomationSystem();
    
    // Verifica argumentos da linha de comando
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
      case 'start':
        await system.start();
        break;
        
      case 'stop':
        await system.stop();
        break;
        
      case 'restart':
        await system.restart();
        break;
        
      case 'status':
        const status = system.getStatus();
        console.log(JSON.stringify(status, null, 2));
        process.exit(0);
        break;
        
      case 'init':
        await system.initialize();
        console.log('Sistema inicializado com sucesso');
        process.exit(0);
        break;
        
      default:
        // Inicia o sistema por padrão
        await system.start();
        break;
    }
    
  } catch (error) {
    console.error('Erro fatal:', error.message);
    process.exit(1);
  }
}

// Executa se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = InvoiceAutomationSystem;
