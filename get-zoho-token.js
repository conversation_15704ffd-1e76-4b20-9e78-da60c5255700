#!/usr/bin/env node

/**
 * Script para obter refresh token do Zoho Mail via linha de comando
 * Use este script se o assistente web não funcionar
 */

const https = require('https');
const querystring = require('querystring');
const fs = require('fs');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise(resolve => rl.question(prompt, resolve));
}

function makeHttpsRequest(options, postData) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          resolve(data);
        }
      });
    });
    
    req.on('error', reject);
    if (postData) req.write(postData);
    req.end();
  });
}

async function getZohoToken() {
  console.log('🔑 Obter Refresh Token do Zoho Mail');
  console.log('===================================');
  console.log('');
  
  try {
    // Passo 1: Obter credenciais
    console.log('📋 Passo 1: Insira suas credenciais do Zoho API Console');
    console.log('(Obtenha em: https://api-console.zoho.com/)');
    console.log('');
    
    const clientId = await question('Client ID: ');
    const clientSecret = await question('Client Secret: ');
    
    if (!clientId || !clientSecret) {
      console.log('❌ Client ID e Client Secret são obrigatórios!');
      process.exit(1);
    }
    
    // Passo 2: Gerar URL de autorização
    console.log('');
    console.log('📋 Passo 2: URL de Autorização');
    const authUrl = `https://accounts.zoho.com/oauth/v2/auth?scope=ZohoMail.messages.READ&client_id=${clientId}&response_type=code&redirect_uri=http://localhost:3001/callback&access_type=offline`;
    
    console.log('');
    console.log('🔗 Acesse esta URL no seu navegador:');
    console.log('');
    console.log(authUrl);
    console.log('');
    console.log('📋 Após autorizar, você será redirecionado para uma página com erro (normal!)');
    console.log('📋 Copie o CÓDIGO da URL (parâmetro "code=...")');
    console.log('');
    
    // Passo 3: Obter código de autorização
    const authCode = await question('Cole o código de autorização aqui: ');
    
    if (!authCode) {
      console.log('❌ Código de autorização é obrigatório!');
      process.exit(1);
    }
    
    // Passo 4: Obter refresh token
    console.log('');
    console.log('⏳ Obtendo refresh token...');
    
    const postData = querystring.stringify({
      grant_type: 'authorization_code',
      client_id: clientId,
      client_secret: clientSecret,
      redirect_uri: 'http://localhost:3001/callback',
      code: authCode
    });
    
    const options = {
      hostname: 'accounts.zoho.com',
      path: '/oauth/v2/token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': postData.length
      }
    };
    
    const result = await makeHttpsRequest(options, postData);
    
    if (result.refresh_token) {
      console.log('');
      console.log('✅ Refresh token obtido com sucesso!');
      console.log('');
      console.log('📋 Suas credenciais:');
      console.log('==================');
      console.log(`ZOHO_CLIENT_ID=${clientId}`);
      console.log(`ZOHO_CLIENT_SECRET=${clientSecret}`);
      console.log(`ZOHO_REFRESH_TOKEN=${result.refresh_token}`);
      console.log('');
      
      // Salvar no .env
      const saveToEnv = await question('💾 Salvar automaticamente no arquivo .env? (s/n): ');
      
      if (saveToEnv.toLowerCase() === 's' || saveToEnv.toLowerCase() === 'sim') {
        try {
          let envContent = '';
          if (fs.existsSync('.env')) {
            envContent = fs.readFileSync('.env', 'utf8');
          }
          
          const updates = {
            ZOHO_CLIENT_ID: clientId,
            ZOHO_CLIENT_SECRET: clientSecret,
            ZOHO_REFRESH_TOKEN: result.refresh_token
          };
          
          for (const [key, value] of Object.entries(updates)) {
            const regex = new RegExp(`^${key}=.*$`, 'm');
            if (envContent.match(regex)) {
              envContent = envContent.replace(regex, `${key}=${value}`);
            } else {
              envContent += `\n${key}=${value}`;
            }
          }
          
          fs.writeFileSync('.env', envContent);
          console.log('✅ Credenciais salvas no arquivo .env!');
        } catch (error) {
          console.log('❌ Erro ao salvar .env:', error.message);
          console.log('📋 Copie as credenciais acima manualmente para o arquivo .env');
        }
      }
      
      console.log('');
      console.log('🎉 Configuração concluída!');
      console.log('');
      console.log('🚀 Próximos passos:');
      console.log('1. Verifique se as credenciais estão no arquivo .env');
      console.log('2. Execute: npm start');
      console.log('3. Acesse: http://localhost:3000');
      console.log('');
      
    } else {
      console.log('');
      console.log('❌ Erro ao obter refresh token:');
      console.log(JSON.stringify(result, null, 2));
      console.log('');
      console.log('🔍 Possíveis causas:');
      console.log('- Código de autorização expirado (tente novamente)');
      console.log('- Client ID ou Client Secret incorretos');
      console.log('- Redirect URI não configurado corretamente no Zoho');
      console.log('');
    }
    
  } catch (error) {
    console.log('');
    console.log('❌ Erro:', error.message);
    console.log('');
    console.log('🆘 Tente:');
    console.log('1. Verificar sua conexão com a internet');
    console.log('2. Verificar se as credenciais estão corretas');
    console.log('3. Gerar um novo código de autorização');
    console.log('');
  } finally {
    rl.close();
  }
}

// Verificar se está sendo executado diretamente
if (require.main === module) {
  getZohoToken();
}

module.exports = { getZohoToken };
