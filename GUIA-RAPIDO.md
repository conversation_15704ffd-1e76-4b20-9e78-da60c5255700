# 🚀 Guia R<PERSON>pido - Como Usar o Sistema

## ✅ O que você já tem funcionando:

1. **✅ Sistema básico rodando** em http://localhost:3000
2. **✅ Diretórios criados** (data/input, data/output, data/logs)
3. **✅ Arquivo .env configurado**

## 📋 Próximos Passos (Passo a Passo):

### **Passo 1: Configure suas credenciais**

Edite o arquivo `.env` e substitua as seguintes linhas:

```env
# Substitua estas linhas no arquivo .env:
ZOHO_CLIENT_ID=seu_client_id_aqui
ZOHO_CLIENT_SECRET=seu_client_secret_aqui  
ZOHO_REFRESH_TOKEN=seu_refresh_token_aqui

# Configure o e-mail para monitorar:
SENDER_EMAIL=<EMAIL>
ATTACHMENT_KEYWORD=angus
```

### **Passo 2: Obter credenciais do Zoho Mail**

1. Acesse: https://api-console.zoho.com/
2. Clique em "Add Client"
3. Escolha "Server-based Applications"
4. Preencha:
   - Client Name: "Invoice Automation"
   - Homepage URL: http://localhost:3000
   - Authorized Redirect URIs: http://localhost:3000/callback
5. Anote o **Client ID** e **Client Secret**

### **Passo 3: Gerar Refresh Token**

1. Acesse esta URL (substitua SEU_CLIENT_ID):
```
https://accounts.zoho.com/oauth/v2/auth?scope=ZohoMail.messages.READ&client_id=SEU_CLIENT_ID&response_type=code&redirect_uri=http://localhost:3000/callback&access_type=offline
```

2. Autorize o acesso
3. Copie o **code** da URL de retorno
4. Use o code para obter o refresh token (vou te ajudar com isso)

### **Passo 4: Testar sem dependências completas**

Por enquanto, você pode usar o sistema básico que está rodando para:
- ✅ Ver a interface
- ✅ Configurar credenciais
- ✅ Testar a estrutura

### **Passo 5: Instalar dependências (quando resolver o problema do npm)**

Quando conseguirmos resolver o problema do npm, você poderá:

```bash
# Limpar cache (se necessário)
npm cache clean --force

# Instalar dependências
npm install

# Iniciar sistema completo
npm start
```

## 🔧 **Testando o Sistema Agora (Modo Simples)**

1. **✅ Já está rodando** - Acesse http://localhost:3000
2. **Adicione arquivos de teste** na pasta `data/input/`
3. **Configure o .env** com suas credenciais
4. **Monitore os logs** na pasta `data/logs/`

## 📁 **Como Usar os Diretórios**

```
data/
├── input/     ← Coloque PDFs e Excel aqui para testar
├── output/    ← Arquivos convertidos aparecerão aqui  
└── logs/      ← Logs do sistema
```

## 🆘 **Resolvendo Problemas do NPM**

Se o `npm install` não funcionar, tente:

```bash
# Opção 1: Usar yarn (se tiver instalado)
yarn install

# Opção 2: Instalar dependências uma por uma
npm install express --save
npm install cors --save
npm install dotenv --save

# Opção 3: Usar npx para executar sem instalar
npx express-generator temp-app
```

## 🎯 **Funcionalidades Disponíveis Agora**

### ✅ **Funcionando (Modo Simples):**
- Interface web básica
- Estrutura de diretórios
- Configuração de ambiente
- Servidor web

### 🔄 **Disponível após npm install:**
- Monitoramento automático de e-mail
- Extração de dados de PDF/Excel
- Conversão para QuickBooks
- Notificações por e-mail/Slack
- Interface web completa

## 📞 **Precisa de Ajuda?**

Me diga:
1. **Consegue ver a página** em http://localhost:3000?
2. **Tem as credenciais** do Zoho Mail?
3. **Quer que eu ajude** a configurar algo específico?

## 🚀 **Teste Rápido**

Para testar se está funcionando:

1. **Coloque um arquivo PDF** em `data/input/`
2. **Verifique se aparece** na interface web
3. **Configure as credenciais** no .env
4. **Quando instalar as dependências**, o sistema processará automaticamente

---

**🎉 Parabéns! O sistema básico está funcionando!**

Agora você pode configurar as credenciais e testar a funcionalidade básica. Quando resolvermos o problema do npm, terá acesso a todas as funcionalidades avançadas.
