{"name": "invoice-automation", "version": "1.0.0", "description": "Sistema completo de automação de invoices com integração Zoho Mail e QuickBooks", "main": "src/index.js", "scripts": {"start": "node src/index-simple.js", "start:full": "node src/index.js", "dev": "node start-simple.js", "web": "node web/server.js", "zoho": "node zoho-fix.js", "test-system": "node test-system.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "fs-extra": "^11.1.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "axios": "^1.6.2", "nodemailer": "^6.9.7", "pdf-parse": "^1.1.1", "tesseract.js": "^5.0.3", "exceljs": "^4.4.0", "csv-writer": "^1.6.0", "node-cron": "^3.0.3", "multer": "^1.4.5-lts.1", "moment": "^2.29.4", "lodash": "^4.17.21"}, "devDependencies": {"jest": "^29.7.0", "eslint": "^8.55.0", "nodemon": "^3.0.2", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "babel-jest": "^29.7.0"}, "engines": {"node": ">=16.0.0"}, "keywords": ["invoice", "automation", "zoho", "quickbooks", "pdf", "excel", "email"], "author": "Invoice Automation System", "license": "MIT"}