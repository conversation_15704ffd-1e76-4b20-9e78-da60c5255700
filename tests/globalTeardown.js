const fs = require('fs-extra');
const path = require('path');

module.exports = async () => {
  console.log('Cleaning up global test environment...');
  
  try {
    // Remove global test directories
    const globalTestDir = path.join(__dirname, '../test-data');
    if (await fs.pathExists(globalTestDir)) {
      await fs.remove(globalTestDir);
      console.log('Global test data directory removed');
    }
    
    // Remove any temporary files created during tests
    const tempDirs = [
      path.join(__dirname, 'temp'),
      path.join(__dirname, '../temp'),
      path.join(__dirname, '../coverage/temp')
    ];
    
    for (const dir of tempDirs) {
      if (await fs.pathExists(dir)) {
        await fs.remove(dir);
        console.log(`Temporary directory removed: ${dir}`);
      }
    }
    
    console.log('Global test environment cleanup completed');
  } catch (error) {
    console.warn('Error during global test cleanup:', error.message);
  }
};
