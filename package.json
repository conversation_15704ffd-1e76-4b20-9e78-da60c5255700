{"name": "invoice-automation-zoho-quickbooks", "version": "1.0.0", "description": "Sistema automatizado para processar invoices do Zoho Mail e converter para QuickBooks", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "web": "node web/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:ci": "jest --ci --coverage --watchAll=false", "lint": "eslint src/ web/ tests/", "lint:fix": "eslint src/ web/ tests/ --fix"}, "keywords": ["automation", "zoho-mail", "quickbooks", "invoice-processing", "ocr", "pdf-extraction"], "author": "LB Accounting", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "axios": "^1.5.0", "nodemailer": "^6.9.4", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1", "pdf-parse": "^1.1.1", "tesseract.js": "^4.1.1", "exceljs": "^4.3.0", "csv-writer": "^1.6.0", "multer": "^1.4.5-lts.1", "cron": "^2.4.4", "moment": "^2.29.4", "lodash": "^4.17.21", "fs-extra": "^11.1.1", "path": "^0.12.7", "mime-types": "^2.1.35"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@jest/globals": "^29.7.0", "babel-jest": "^29.7.0", "eslint": "^8.47.0", "jest": "^29.6.2", "jest-junit": "^16.0.0", "jest-watch-typeahead": "^2.2.2", "supertest": "^6.3.3", "nodemon": "^3.0.1", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-n": "^16.0.2", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/lbaccounting/invoice-automation.git"}, "bugs": {"url": "https://github.com/lbaccounting/invoice-automation/issues"}, "homepage": "https://github.com/lbaccounting/invoice-automation#readme"}