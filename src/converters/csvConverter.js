const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const path = require('path');
const fs = require('fs-extra');
const config = require('../config/config');
const { logger, logError, logProcessing } = require('../config/logger');

/**
 * Conversor para formato CSV compatível com QuickBooks Online
 */
class CSVConverter {
  constructor() {
    this.outputPath = config.storage.outputPath;
  }

  /**
   * Converte dados de invoice para formato CSV do QuickBooks Online
   */
  async convertToCSV(invoiceData, outputFileName = null) {
    try {
      logProcessing('csv_conversion_started', {
        invoiceNumber: invoiceData.invoiceNumber,
        vendorName: invoiceData.vendorName,
        total: invoiceData.total
      });

      // Gera nome do arquivo se não fornecido
      if (!outputFileName) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        outputFileName = `invoice_${invoiceData.invoiceNumber || timestamp}_qbo.csv`;
      }

      const outputFilePath = path.join(this.outputPath, outputFileName);
      await fs.ensureDir(this.outputPath);

      // Prepara dados para CSV baseado no tipo de transação
      const csvData = this.prepareCSVData(invoiceData);

      // Cria o arquivo CSV
      await this.writeCSVFile(csvData, outputFilePath);

      logProcessing('csv_conversion_completed', {
        invoiceNumber: invoiceData.invoiceNumber,
        outputFile: outputFilePath,
        recordsCount: csvData.length
      });

      return outputFilePath;
    } catch (error) {
      logError(error, { context: 'convertToCSV', invoiceData });
      throw error;
    }
  }

  /**
   * Prepara os dados para o formato CSV do QuickBooks Online
   */
  prepareCSVData(invoiceData) {
    const csvRecords = [];

    // Formato para Bills (Contas a Pagar)
    if (invoiceData.lineItems && invoiceData.lineItems.length > 0) {
      // Para cada item de linha, cria um registro
      invoiceData.lineItems.forEach((item, index) => {
        csvRecords.push({
          '*Vendor': invoiceData.vendorName || config.quickbooks.defaultVendorName,
          '*Date': this.formatDateForCSV(invoiceData.invoiceDate),
          '*Account': item.category || config.quickbooks.defaultExpenseCategory,
          'Description': item.description || 'Angus Sprinkler Repair',
          '*Amount': this.formatAmountForCSV(item.amount || invoiceData.total || 0),
          'Billable': 'No',
          'Tax': '',
          'Customer': '',
          'Memo': `Invoice: ${invoiceData.invoiceNumber || 'N/A'}`,
          'Class': ''
        });
      });
    } else {
      // Se não há itens de linha, cria um registro único
      csvRecords.push({
        '*Vendor': invoiceData.vendorName || config.quickbooks.defaultVendorName,
        '*Date': this.formatDateForCSV(invoiceData.invoiceDate),
        '*Account': config.quickbooks.defaultExpenseCategory,
        'Description': 'Angus Sprinkler Repair',
        '*Amount': this.formatAmountForCSV(invoiceData.total || 0),
        'Billable': 'No',
        'Tax': '',
        'Customer': '',
        'Memo': `Invoice: ${invoiceData.invoiceNumber || 'N/A'}`,
        'Class': ''
      });
    }

    return csvRecords;
  }

  /**
   * Escreve o arquivo CSV
   */
  async writeCSVFile(csvData, outputFilePath) {
    const csvWriter = createCsvWriter({
      path: outputFilePath,
      header: [
        { id: '*Vendor', title: '*Vendor' },
        { id: '*Date', title: '*Date' },
        { id: '*Account', title: '*Account' },
        { id: 'Description', title: 'Description' },
        { id: '*Amount', title: '*Amount' },
        { id: 'Billable', title: 'Billable' },
        { id: 'Tax', title: 'Tax' },
        { id: 'Customer', title: 'Customer' },
        { id: 'Memo', title: 'Memo' },
        { id: 'Class', title: 'Class' }
      ],
      encoding: 'utf8'
    });

    await csvWriter.writeRecords(csvData);
  }

  /**
   * Converte dados para formato de Expense (Despesas)
   */
  async convertToExpenseCSV(invoiceData, outputFileName = null) {
    try {
      logProcessing('expense_csv_conversion_started', {
        invoiceNumber: invoiceData.invoiceNumber,
        vendorName: invoiceData.vendorName,
        total: invoiceData.total
      });

      if (!outputFileName) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        outputFileName = `expense_${invoiceData.invoiceNumber || timestamp}_qbo.csv`;
      }

      const outputFilePath = path.join(this.outputPath, outputFileName);
      await fs.ensureDir(this.outputPath);

      // Prepara dados para formato de despesa
      const csvData = [{
        '*Date': this.formatDateForCSV(invoiceData.invoiceDate),
        '*Amount': this.formatAmountForCSV(invoiceData.total || 0),
        '*Account': config.quickbooks.defaultExpenseCategory,
        'Description': `Angus Sprinkler Repair - Invoice: ${invoiceData.invoiceNumber || 'N/A'}`,
        'Vendor': invoiceData.vendorName || config.quickbooks.defaultVendorName,
        'Category': config.quickbooks.defaultExpenseCategory,
        'Payment Method': 'Check',
        'Reference No': invoiceData.invoiceNumber || '',
        'Memo': `Email processed: ${invoiceData.emailMetadata?.emailDate || new Date().toISOString()}`
      }];

      // Cria o arquivo CSV para despesas
      const csvWriter = createCsvWriter({
        path: outputFilePath,
        header: [
          { id: '*Date', title: '*Date' },
          { id: '*Amount', title: '*Amount' },
          { id: '*Account', title: '*Account' },
          { id: 'Description', title: 'Description' },
          { id: 'Vendor', title: 'Vendor' },
          { id: 'Category', title: 'Category' },
          { id: 'Payment Method', title: 'Payment Method' },
          { id: 'Reference No', title: 'Reference No' },
          { id: 'Memo', title: 'Memo' }
        ],
        encoding: 'utf8'
      });

      await csvWriter.writeRecords(csvData);

      logProcessing('expense_csv_conversion_completed', {
        invoiceNumber: invoiceData.invoiceNumber,
        outputFile: outputFilePath
      });

      return outputFilePath;
    } catch (error) {
      logError(error, { context: 'convertToExpenseCSV', invoiceData });
      throw error;
    }
  }

  /**
   * Formata data para o formato esperado pelo QuickBooks (MM/DD/YYYY)
   */
  formatDateForCSV(dateString) {
    try {
      if (!dateString) {
        dateString = new Date().toISOString().split('T')[0];
      }

      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return new Date().toLocaleDateString('en-US');
      }

      return date.toLocaleDateString('en-US');
    } catch (error) {
      logError(error, { context: 'formatDateForCSV', dateString });
      return new Date().toLocaleDateString('en-US');
    }
  }

  /**
   * Formata valor monetário para CSV
   */
  formatAmountForCSV(amount) {
    try {
      if (!amount || isNaN(amount)) {
        return '0.00';
      }
      return parseFloat(amount).toFixed(2);
    } catch (error) {
      logError(error, { context: 'formatAmountForCSV', amount });
      return '0.00';
    }
  }

  /**
   * Gera múltiplos formatos CSV para diferentes tipos de importação
   */
  async generateAllCSVFormats(invoiceData) {
    const results = {};

    try {
      // Formato Bills (Contas a Pagar)
      results.bills = await this.convertToCSV(invoiceData);

      // Formato Expenses (Despesas)
      results.expenses = await this.convertToExpenseCSV(invoiceData);

      logProcessing('all_csv_formats_generated', {
        invoiceNumber: invoiceData.invoiceNumber,
        formats: Object.keys(results)
      });

      return results;
    } catch (error) {
      logError(error, { context: 'generateAllCSVFormats', invoiceData });
      throw error;
    }
  }

  /**
   * Valida dados antes da conversão
   */
  validateInvoiceData(invoiceData) {
    const errors = [];

    if (!invoiceData.vendorName && !config.quickbooks.defaultVendorName) {
      errors.push('Vendor name is required');
    }

    if (!invoiceData.total && (!invoiceData.lineItems || invoiceData.lineItems.length === 0)) {
      errors.push('Total amount or line items are required');
    }

    if (invoiceData.total && isNaN(parseFloat(invoiceData.total))) {
      errors.push('Total amount must be a valid number');
    }

    return errors;
  }
}

module.exports = CSVConverter;
