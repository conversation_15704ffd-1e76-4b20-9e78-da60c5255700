const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const fs = require('fs-extra');
const config = require('./config');

// Garantir que o diretório de logs existe
fs.ensureDirSync(config.storage.logsPath);

/**
 * Configuração do sistema de logs
 */
const logger = winston.createLogger({
  level: config.app.logLevel,
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { 
    service: config.app.name,
    version: config.app.version
  },
  transports: [
    // Log de aplicação geral
    new DailyRotateFile({
      filename: path.join(config.storage.logsPath, 'application-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: `${config.backup.logRetentionDays}d`,
      level: 'info'
    }),

    // Log de erros
    new DailyRotateFile({
      filename: path.join(config.storage.logsPath, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: `${config.backup.logRetentionDays}d`,
      level: 'error'
    }),

    // Log de processamento de e-mails
    new DailyRotateFile({
      filename: path.join(config.storage.logsPath, 'email-processing-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: `${config.backup.logRetentionDays}d`,
      level: 'debug',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          return `${timestamp} [${level.toUpperCase()}] ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
        })
      )
    })
  ]
});

// Em desenvolvimento, também log no console
if (config.app.env !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        const metaStr = Object.keys(meta).length ? `\n${JSON.stringify(meta, null, 2)}` : '';
        return `${timestamp} [${level}] ${message}${metaStr}`;
      })
    )
  }));
}

/**
 * Logger específico para processamento de e-mails
 */
const emailLogger = winston.createLogger({
  level: 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { 
    service: 'email-processor',
    version: config.app.version
  },
  transports: [
    new DailyRotateFile({
      filename: path.join(config.storage.logsPath, 'email-processing-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: `${config.backup.logRetentionDays}d`
    })
  ]
});

/**
 * Logger específico para conversões
 */
const conversionLogger = winston.createLogger({
  level: 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { 
    service: 'conversion-processor',
    version: config.app.version
  },
  transports: [
    new DailyRotateFile({
      filename: path.join(config.storage.logsPath, 'conversions-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: `${config.backup.logRetentionDays}d`
    })
  ]
});

/**
 * Função para criar logs estruturados de processamento
 */
function logProcessing(type, data) {
  const logData = {
    type,
    timestamp: new Date().toISOString(),
    ...data
  };

  switch (type) {
    case 'email_received':
    case 'email_processed':
    case 'attachment_found':
      emailLogger.info(`${type.toUpperCase()}`, logData);
      break;
    case 'conversion_started':
    case 'conversion_completed':
    case 'conversion_failed':
      conversionLogger.info(`${type.toUpperCase()}`, logData);
      break;
    default:
      logger.info(`PROCESSING_${type.toUpperCase()}`, logData);
  }
}

/**
 * Função para logs de erro com contexto
 */
function logError(error, context = {}) {
  logger.error('ERROR_OCCURRED', {
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name
    },
    context,
    timestamp: new Date().toISOString()
  });
}

/**
 * Função para logs de debug
 */
function logDebug(message, data = {}) {
  if (config.debug.saveDebugFiles) {
    logger.debug(message, {
      ...data,
      timestamp: new Date().toISOString()
    });
  }
}

module.exports = {
  logger,
  emailLogger,
  conversionLogger,
  logProcessing,
  logError,
  logDebug
};
