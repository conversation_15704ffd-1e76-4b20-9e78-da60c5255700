const cron = require('cron');
const ZohoMailClient = require('./zohoClient');
const config = require('../config/config');
const { logger, logError, logProcessing } = require('../config/logger');
const fs = require('fs-extra');
const path = require('path');

/**
 * Monitor de e-mails que verifica periodicamente por novas invoices
 */
class EmailMonitor {
  constructor() {
    this.zohoClient = new ZohoMailClient();
    this.isRunning = false;
    this.cronJob = null;
    this.lastCheckTime = null;
    this.processedEmails = new Set(); // Para evitar reprocessamento
  }

  /**
   * Inicia o monitoramento de e-mails
   */
  start() {
    if (this.isRunning) {
      logger.warn('Email monitor is already running');
      return;
    }

    // Cria o job cron baseado no intervalo configurado
    const cronPattern = `*/${config.emailFilters.checkInterval} * * * *`; // A cada X minutos
    
    this.cronJob = new cron.CronJob(cronPattern, async () => {
      await this.checkEmails();
    }, null, false, 'America/New_York');

    this.cronJob.start();
    this.isRunning = true;

    logger.info('Email monitor started', {
      checkInterval: config.emailFilters.checkInterval,
      cronPattern,
      workingHours: config.emailFilters.workingHours,
      workingDays: config.emailFilters.workingDays
    });
  }

  /**
   * Para o monitoramento de e-mails
   */
  stop() {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
    }
    this.isRunning = false;
    logger.info('Email monitor stopped');
  }

  /**
   * Verifica e-mails manualmente (uma vez)
   */
  async checkEmails() {
    // Verifica se está em horário de funcionamento
    if (!config.isWorkingTime()) {
      logger.debug('Outside working hours, skipping email check');
      return;
    }

    if (config.debug.testMode) {
      logger.info('Running in test mode - no real email processing');
      return;
    }

    try {
      logProcessing('email_check_started', {
        lastCheckTime: this.lastCheckTime
      });

      const matchingEmails = await this.zohoClient.findMatchingEmails();
      
      if (matchingEmails.length === 0) {
        logger.debug('No matching emails found');
        return;
      }

      logger.info(`Found ${matchingEmails.length} matching emails`);

      for (const email of matchingEmails) {
        await this.processEmail(email);
      }

      this.lastCheckTime = new Date();

      logProcessing('email_check_completed', {
        emailsProcessed: matchingEmails.length,
        checkTime: this.lastCheckTime
      });

    } catch (error) {
      logError(error, { context: 'checkEmails' });
    }
  }

  /**
   * Processa um e-mail individual
   */
  async processEmail(emailDetails) {
    const emailId = emailDetails.messageId;

    // Evita reprocessamento
    if (this.processedEmails.has(emailId)) {
      logger.debug(`Email ${emailId} already processed, skipping`);
      return;
    }

    try {
      logProcessing('email_processing_started', {
        emailId,
        subject: emailDetails.subject,
        fromAddress: emailDetails.fromAddress,
        receivedTime: emailDetails.receivedTime
      });

      // Obtém anexos relevantes
      const relevantAttachments = this.zohoClient.getRelevantAttachments(emailDetails);

      if (relevantAttachments.length === 0) {
        logger.warn(`No relevant attachments found in email ${emailId}`);
        return;
      }

      // Processa cada anexo relevante
      for (const attachment of relevantAttachments) {
        await this.processAttachment(emailDetails, attachment);
      }

      // Marca como processado
      this.processedEmails.add(emailId);

      logProcessing('email_processing_completed', {
        emailId,
        attachmentsProcessed: relevantAttachments.length
      });

    } catch (error) {
      logError(error, { 
        context: 'processEmail', 
        emailId,
        subject: emailDetails.subject 
      });
    }
  }

  /**
   * Processa um anexo específico
   */
  async processAttachment(emailDetails, attachment) {
    try {
      logProcessing('attachment_processing_started', {
        emailId: emailDetails.messageId,
        attachmentId: attachment.attachmentId,
        fileName: attachment.attachmentName,
        fileSize: attachment.size
      });

      // Baixa o anexo
      const attachmentData = await this.zohoClient.downloadAttachment(
        emailDetails.messageId,
        attachment.attachmentId
      );

      // Salva o arquivo localmente
      const fileName = this.generateUniqueFileName(attachment.attachmentName);
      const filePath = path.join(config.storage.inputPath, fileName);
      
      await fs.ensureDir(config.storage.inputPath);
      await fs.writeFile(filePath, attachmentData);

      // Cria metadados do arquivo
      const metadata = {
        originalFileName: attachment.attachmentName,
        emailId: emailDetails.messageId,
        emailSubject: emailDetails.subject,
        emailFrom: emailDetails.fromAddress,
        emailDate: emailDetails.receivedTime,
        downloadDate: new Date().toISOString(),
        fileSize: attachment.size,
        filePath: filePath
      };

      // Salva metadados
      const metadataPath = filePath + '.metadata.json';
      await fs.writeJson(metadataPath, metadata, { spaces: 2 });

      logProcessing('attachment_processing_completed', {
        emailId: emailDetails.messageId,
        attachmentId: attachment.attachmentId,
        fileName: attachment.attachmentName,
        savedPath: filePath
      });

      // Dispara o processamento do arquivo
      await this.triggerFileProcessing(filePath, metadata);

    } catch (error) {
      logError(error, { 
        context: 'processAttachment',
        emailId: emailDetails.messageId,
        attachmentId: attachment.attachmentId,
        fileName: attachment.attachmentName
      });
    }
  }

  /**
   * Gera um nome único para o arquivo
   */
  generateUniqueFileName(originalName) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const extension = path.extname(originalName);
    const baseName = path.basename(originalName, extension);
    return `${baseName}_${timestamp}${extension}`;
  }

  /**
   * Dispara o processamento do arquivo baixado
   */
  async triggerFileProcessing(filePath, metadata) {
    try {
      // Aqui será integrado com o sistema de processamento de arquivos
      // Por enquanto, apenas registra que o arquivo está pronto para processamento
      
      logProcessing('file_ready_for_processing', {
        filePath,
        metadata
      });

      // TODO: Integrar com o módulo de extração de dados
      // const extractor = require('../extractors/invoiceExtractor');
      // await extractor.processFile(filePath, metadata);

    } catch (error) {
      logError(error, { 
        context: 'triggerFileProcessing',
        filePath,
        metadata
      });
    }
  }

  /**
   * Obtém estatísticas do monitor
   */
  getStats() {
    return {
      isRunning: this.isRunning,
      lastCheckTime: this.lastCheckTime,
      processedEmailsCount: this.processedEmails.size,
      checkInterval: config.emailFilters.checkInterval,
      workingHours: config.emailFilters.workingHours,
      workingDays: config.emailFilters.workingDays
    };
  }

  /**
   * Limpa o cache de e-mails processados
   */
  clearProcessedCache() {
    this.processedEmails.clear();
    logger.info('Processed emails cache cleared');
  }
}

module.exports = EmailMonitor;
