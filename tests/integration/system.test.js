const { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, jest } = require('@jest/globals');
const fs = require('fs-extra');
const path = require('path');
const InvoiceAutomationSystem = require('../../src/index');

describe('Invoice Automation System Integration', () => {
  let system;
  let testDataDir;
  let originalEnv;

  beforeAll(async () => {
    // Backup original environment
    originalEnv = { ...process.env };
    
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.WEB_PORT = '3001'; // Use different port for tests
    process.env.ZOHO_AUTO_START = 'false'; // Don't auto-start email monitor in tests
    
    // Create test data directory
    testDataDir = path.join(__dirname, '../temp/integration');
    await fs.ensureDir(testDataDir);
    await fs.ensureDir(path.join(testDataDir, 'input'));
    await fs.ensureDir(path.join(testDataDir, 'output'));
    await fs.ensureDir(path.join(testDataDir, 'logs'));
    
    // Override storage paths for testing
    process.env.STORAGE_LOCAL_PATH = testDataDir;
  });

  afterAll(async () => {
    // Restore original environment
    process.env = originalEnv;
    
    // Cleanup test data
    await fs.remove(path.join(__dirname, '../temp'));
  });

  beforeEach(async () => {
    system = new InvoiceAutomationSystem();
  });

  afterEach(async () => {
    if (system && system.isRunning) {
      await system.stop();
    }
  });

  describe('System Initialization', () => {
    it('should initialize system successfully', async () => {
      const result = await system.initialize();
      expect(result).toBe(true);
      expect(system.services.storageManager).toBeDefined();
      expect(system.services.notificationService).toBeDefined();
      expect(system.services.quickbooksConverter).toBeDefined();
      expect(system.services.emailMonitor).toBeDefined();
    });

    it('should validate configuration during initialization', async () => {
      await system.initialize();
      
      // Should complete without throwing, even with missing config
      expect(system.services).toBeDefined();
    });

    it('should create required directories', async () => {
      await system.initialize();
      
      const requiredDirs = [
        path.join(testDataDir, 'input'),
        path.join(testDataDir, 'output'),
        path.join(testDataDir, 'logs')
      ];

      for (const dir of requiredDirs) {
        expect(await fs.pathExists(dir)).toBe(true);
      }
    });
  });

  describe('System Lifecycle', () => {
    it('should start and stop system successfully', async () => {
      await system.initialize();
      
      // Start system
      const startResult = await system.start();
      expect(startResult).toBe(true);
      expect(system.isRunning).toBe(true);
      
      // Stop system
      const stopResult = await system.stop();
      expect(stopResult).toBe(true);
      expect(system.isRunning).toBe(false);
    });

    it('should handle multiple start calls gracefully', async () => {
      await system.initialize();
      
      await system.start();
      expect(system.isRunning).toBe(true);
      
      // Second start should not throw
      await system.start();
      expect(system.isRunning).toBe(true);
    });

    it('should handle stop when not running', async () => {
      await system.initialize();
      
      // Stop when not running should not throw
      const result = await system.stop();
      expect(result).toBe(true);
      expect(system.isRunning).toBe(false);
    });

    it('should restart system successfully', async () => {
      await system.initialize();
      await system.start();
      
      const restartResult = await system.restart();
      expect(restartResult).toBe(undefined); // restart doesn't return value
      expect(system.isRunning).toBe(true);
    });
  });

  describe('System Status', () => {
    it('should return correct status when not running', async () => {
      await system.initialize();
      
      const status = system.getStatus();
      expect(status.isRunning).toBe(false);
      expect(status.startTime).toBeNull();
      expect(status.uptime).toBe(0);
      expect(status.version).toBeDefined();
      expect(status.environment).toBe('test');
      expect(status.services).toBeDefined();
    });

    it('should return correct status when running', async () => {
      await system.initialize();
      await system.start();
      
      const status = system.getStatus();
      expect(status.isRunning).toBe(true);
      expect(status.startTime).toBeDefined();
      expect(status.uptime).toBeGreaterThan(0);
      expect(status.services.storageManager).toBe(true);
      expect(status.services.notificationService).toBe(true);
      expect(status.services.quickbooksConverter).toBe(true);
    });

    it('should track uptime correctly', async () => {
      await system.initialize();
      await system.start();
      
      const status1 = system.getStatus();
      const uptime1 = status1.uptime;
      
      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const status2 = system.getStatus();
      const uptime2 = status2.uptime;
      
      expect(uptime2).toBeGreaterThan(uptime1);
    });
  });

  describe('Service Integration', () => {
    it('should initialize all services correctly', async () => {
      await system.initialize();
      
      expect(system.services.storageManager).toBeDefined();
      expect(system.services.notificationService).toBeDefined();
      expect(system.services.quickbooksConverter).toBeDefined();
      expect(system.services.emailMonitor).toBeDefined();
    });

    it('should start web server when enabled', async () => {
      // Enable web server for this test
      process.env.WEB_ENABLED = 'true';
      
      await system.initialize();
      await system.start();
      
      expect(system.services.webServer).toBeDefined();
      
      // Reset for other tests
      delete process.env.WEB_ENABLED;
    });
  });

  describe('Error Handling', () => {
    it('should handle initialization errors gracefully', async () => {
      // Mock a service to throw during initialization
      const originalStorageManager = require('../../src/storage/storageManager');
      jest.doMock('../../src/storage/storageManager', () => {
        return jest.fn().mockImplementation(() => {
          throw new Error('Mock initialization error');
        });
      });

      const systemWithError = new InvoiceAutomationSystem();
      
      await expect(systemWithError.initialize()).rejects.toThrow('Mock initialization error');
      
      // Restore original module
      jest.dontMock('../../src/storage/storageManager');
    });

    it('should handle start errors gracefully', async () => {
      await system.initialize();
      
      // Mock web server to throw during start
      system.services.webServer = {
        start: jest.fn().mockRejectedValue(new Error('Mock start error'))
      };
      
      await expect(system.start()).rejects.toThrow('Mock start error');
    });

    it('should handle stop errors gracefully', async () => {
      await system.initialize();
      await system.start();
      
      // Mock email monitor to throw during stop
      system.services.emailMonitor.stop = jest.fn().mockRejectedValue(new Error('Mock stop error'));
      
      await expect(system.stop()).rejects.toThrow('Mock stop error');
    });
  });

  describe('Configuration Validation', () => {
    it('should detect configuration issues', async () => {
      await system.initialize();
      
      const issues = await system.validateConfiguration();
      expect(Array.isArray(issues)).toBe(true);
      
      // In test environment, we expect some configuration issues
      expect(issues.length).toBeGreaterThan(0);
    });

    it('should validate required directories', async () => {
      // Remove a required directory
      await fs.remove(path.join(testDataDir, 'input'));
      
      await system.initialize();
      const issues = await system.validateConfiguration();
      
      expect(issues.some(issue => issue.includes('input'))).toBe(true);
      
      // Recreate directory for other tests
      await fs.ensureDir(path.join(testDataDir, 'input'));
    });
  });

  describe('Manual File Processing', () => {
    it('should handle manual file processing request', async () => {
      await system.initialize();
      
      const testFilePath = path.join(testDataDir, 'input', 'test-invoice.pdf');
      await fs.writeFile(testFilePath, 'mock pdf content');
      
      const result = await system.processFile(testFilePath);
      
      // Currently returns not implemented
      expect(result.success).toBe(false);
      expect(result.message).toContain('não implementado');
    });

    it('should handle invalid file path', async () => {
      await system.initialize();
      
      await expect(system.processFile('/invalid/path/file.pdf')).rejects.toThrow();
    });
  });

  describe('Process Signal Handling', () => {
    it('should setup process handlers', async () => {
      await system.initialize();
      
      // Verify that process handlers are set up
      // This is difficult to test directly, but we can check that the system
      // initializes without errors
      expect(system).toBeDefined();
    });
  });

  describe('CLI Commands', () => {
    it('should handle status command', async () => {
      // This would require testing the main() function with mocked process.argv
      // For now, we just verify the getStatus method works
      await system.initialize();
      
      const status = system.getStatus();
      expect(status).toBeDefined();
      expect(typeof status.isRunning).toBe('boolean');
    });
  });

  describe('Memory and Resource Management', () => {
    it('should not leak memory during start/stop cycles', async () => {
      await system.initialize();
      
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform multiple start/stop cycles
      for (let i = 0; i < 3; i++) {
        await system.start();
        await system.stop();
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      
      // Memory should not increase significantly (allow for some variance)
      const memoryIncrease = finalMemory - initialMemory;
      const maxAllowedIncrease = 10 * 1024 * 1024; // 10MB
      
      expect(memoryIncrease).toBeLessThan(maxAllowedIncrease);
    });
  });
});
