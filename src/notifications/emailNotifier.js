const nodemailer = require('nodemailer');
const config = require('../config/config');
const { logger, logError, logProcessing } = require('../config/logger');
const path = require('path');

/**
 * Serviço de notificações por e-mail
 */
class EmailNotifier {
  constructor() {
    this.transporter = null;
    this.isConfigured = false;
    this.initializeTransporter();
  }

  /**
   * Inicializa o transportador de e-mail
   */
  async initializeTransporter() {
    try {
      if (!config.notifications.email.enabled) {
        logger.debug('Email notifications are disabled');
        return;
      }

      if (!config.notifications.email.smtp.auth.user || !config.notifications.email.smtp.auth.pass) {
        logger.warn('Email credentials not configured');
        return;
      }

      this.transporter = nodemailer.createTransporter({
        host: config.notifications.email.smtp.host,
        port: config.notifications.email.smtp.port,
        secure: config.notifications.email.smtp.secure,
        auth: {
          user: config.notifications.email.smtp.auth.user,
          pass: config.notifications.email.smtp.auth.pass
        }
      });

      // Verifica a configuração
      await this.transporter.verify();
      this.isConfigured = true;
      
      logger.info('Email transporter initialized successfully');
    } catch (error) {
      logError(error, { context: 'initializeTransporter' });
      this.isConfigured = false;
    }
  }

  /**
   * Envia notificação de processamento de invoice
   */
  async sendInvoiceProcessedNotification(invoiceData, conversionResults) {
    try {
      if (!this.isConfigured) {
        logger.warn('Email notifier not configured, skipping notification');
        return;
      }

      const subject = `Invoice Processada: ${invoiceData.invoiceNumber || 'N/A'} - ${invoiceData.vendorName}`;
      
      const htmlContent = this.generateInvoiceProcessedHTML(invoiceData, conversionResults);
      const textContent = this.generateInvoiceProcessedText(invoiceData, conversionResults);

      const mailOptions = {
        from: config.notifications.email.smtp.auth.user,
        to: config.notifications.email.recipients,
        subject: subject,
        text: textContent,
        html: htmlContent,
        attachments: this.prepareAttachments(conversionResults)
      };

      const result = await this.transporter.sendMail(mailOptions);

      logProcessing('invoice_notification_sent', {
        invoiceNumber: invoiceData.invoiceNumber,
        recipients: config.notifications.email.recipients,
        messageId: result.messageId
      });

      return result;
    } catch (error) {
      logError(error, { context: 'sendInvoiceProcessedNotification', invoiceData });
      throw error;
    }
  }

  /**
   * Envia notificação de erro
   */
  async sendErrorNotification(error, context = {}) {
    try {
      if (!this.isConfigured) {
        logger.warn('Email notifier not configured, skipping error notification');
        return;
      }

      const subject = `Erro no Sistema de Automação de Invoices - ${new Date().toLocaleDateString()}`;
      
      const htmlContent = this.generateErrorHTML(error, context);
      const textContent = this.generateErrorText(error, context);

      const mailOptions = {
        from: config.notifications.email.smtp.auth.user,
        to: config.notifications.email.recipients,
        subject: subject,
        text: textContent,
        html: htmlContent,
        priority: 'high'
      };

      const result = await this.transporter.sendMail(mailOptions);

      logProcessing('error_notification_sent', {
        error: error.message,
        context,
        recipients: config.notifications.email.recipients,
        messageId: result.messageId
      });

      return result;
    } catch (notificationError) {
      logError(notificationError, { context: 'sendErrorNotification', originalError: error.message });
    }
  }

  /**
   * Envia relatório diário
   */
  async sendDailyReport(reportData) {
    try {
      if (!this.isConfigured) {
        logger.warn('Email notifier not configured, skipping daily report');
        return;
      }

      const date = new Date().toLocaleDateString('pt-BR');
      const subject = `Relatório Diário - Sistema de Automação de Invoices - ${date}`;
      
      const htmlContent = this.generateDailyReportHTML(reportData);
      const textContent = this.generateDailyReportText(reportData);

      const mailOptions = {
        from: config.notifications.email.smtp.auth.user,
        to: config.notifications.email.recipients,
        subject: subject,
        text: textContent,
        html: htmlContent
      };

      const result = await this.transporter.sendMail(mailOptions);

      logProcessing('daily_report_sent', {
        date,
        reportData: {
          processedInvoices: reportData.processedInvoices,
          errors: reportData.errors,
          totalFiles: reportData.totalFiles
        },
        recipients: config.notifications.email.recipients,
        messageId: result.messageId
      });

      return result;
    } catch (error) {
      logError(error, { context: 'sendDailyReport', reportData });
      throw error;
    }
  }

  /**
   * Gera HTML para notificação de invoice processada
   */
  generateInvoiceProcessedHTML(invoiceData, conversionResults) {
    const filesGenerated = [];
    
    if (conversionResults.conversions.csv) {
      Object.entries(conversionResults.conversions.csv).forEach(([type, filePath]) => {
        filesGenerated.push(`<li><strong>${type.toUpperCase()} CSV:</strong> ${path.basename(filePath)}</li>`);
      });
    }
    
    if (conversionResults.conversions.iif) {
      Object.entries(conversionResults.conversions.iif).forEach(([type, filePath]) => {
        filesGenerated.push(`<li><strong>${type.toUpperCase()} IIF:</strong> ${path.basename(filePath)}</li>`);
      });
    }

    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #2c5aa0;">✅ Invoice Processada com Sucesso</h2>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #495057;">Detalhes da Invoice:</h3>
              <ul style="list-style: none; padding: 0;">
                <li><strong>Número:</strong> ${invoiceData.invoiceNumber || 'N/A'}</li>
                <li><strong>Fornecedor:</strong> ${invoiceData.vendorName || 'N/A'}</li>
                <li><strong>Data:</strong> ${invoiceData.invoiceDate || 'N/A'}</li>
                <li><strong>Valor Total:</strong> $${invoiceData.total || '0.00'}</li>
                <li><strong>Itens:</strong> ${invoiceData.lineItems ? invoiceData.lineItems.length : 0}</li>
              </ul>
            </div>

            <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #155724;">Arquivos Gerados:</h3>
              <ul>
                ${filesGenerated.join('')}
              </ul>
              <p><strong>Total de arquivos:</strong> ${conversionResults.summary.totalFiles}</p>
            </div>

            ${conversionResults.summary.errors.length > 0 ? `
              <div style="background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #721c24;">Avisos:</h3>
                <ul>
                  ${conversionResults.summary.errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
              </div>
            ` : ''}

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d;">
              <p>Este e-mail foi gerado automaticamente pelo Sistema de Automação de Invoices.</p>
              <p>Data de processamento: ${new Date().toLocaleString('pt-BR')}</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Gera texto simples para notificação de invoice processada
   */
  generateInvoiceProcessedText(invoiceData, conversionResults) {
    const filesGenerated = [];
    
    if (conversionResults.conversions.csv) {
      Object.entries(conversionResults.conversions.csv).forEach(([type, filePath]) => {
        filesGenerated.push(`- ${type.toUpperCase()} CSV: ${path.basename(filePath)}`);
      });
    }
    
    if (conversionResults.conversions.iif) {
      Object.entries(conversionResults.conversions.iif).forEach(([type, filePath]) => {
        filesGenerated.push(`- ${type.toUpperCase()} IIF: ${path.basename(filePath)}`);
      });
    }

    return `
INVOICE PROCESSADA COM SUCESSO

Detalhes da Invoice:
- Número: ${invoiceData.invoiceNumber || 'N/A'}
- Fornecedor: ${invoiceData.vendorName || 'N/A'}
- Data: ${invoiceData.invoiceDate || 'N/A'}
- Valor Total: $${invoiceData.total || '0.00'}
- Itens: ${invoiceData.lineItems ? invoiceData.lineItems.length : 0}

Arquivos Gerados:
${filesGenerated.join('\n')}

Total de arquivos: ${conversionResults.summary.totalFiles}

${conversionResults.summary.errors.length > 0 ? `
Avisos:
${conversionResults.summary.errors.map(error => `- ${error}`).join('\n')}
` : ''}

---
Este e-mail foi gerado automaticamente pelo Sistema de Automação de Invoices.
Data de processamento: ${new Date().toLocaleString('pt-BR')}
    `.trim();
  }

  /**
   * Gera HTML para notificação de erro
   */
  generateErrorHTML(error, context) {
    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #dc3545;">❌ Erro no Sistema de Automação</h2>
            
            <div style="background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #721c24;">Detalhes do Erro:</h3>
              <p><strong>Mensagem:</strong> ${error.message}</p>
              <p><strong>Tipo:</strong> ${error.name || 'Error'}</p>
              <p><strong>Data/Hora:</strong> ${new Date().toLocaleString('pt-BR')}</p>
            </div>

            ${context && Object.keys(context).length > 0 ? `
              <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #856404;">Contexto:</h3>
                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto;">${JSON.stringify(context, null, 2)}</pre>
              </div>
            ` : ''}

            ${error.stack ? `
              <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #495057;">Stack Trace:</h3>
                <pre style="font-size: 11px; overflow-x: auto;">${error.stack}</pre>
              </div>
            ` : ''}

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d;">
              <p>Este e-mail foi gerado automaticamente pelo Sistema de Automação de Invoices.</p>
              <p>Por favor, verifique os logs do sistema para mais detalhes.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Gera texto simples para notificação de erro
   */
  generateErrorText(error, context) {
    return `
ERRO NO SISTEMA DE AUTOMAÇÃO

Detalhes do Erro:
- Mensagem: ${error.message}
- Tipo: ${error.name || 'Error'}
- Data/Hora: ${new Date().toLocaleString('pt-BR')}

${context && Object.keys(context).length > 0 ? `
Contexto:
${JSON.stringify(context, null, 2)}
` : ''}

${error.stack ? `
Stack Trace:
${error.stack}
` : ''}

---
Este e-mail foi gerado automaticamente pelo Sistema de Automação de Invoices.
Por favor, verifique os logs do sistema para mais detalhes.
    `.trim();
  }

  /**
   * Gera HTML para relatório diário
   */
  generateDailyReportHTML(reportData) {
    const date = new Date().toLocaleDateString('pt-BR');
    
    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #2c5aa0;">📊 Relatório Diário - ${date}</h2>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #495057;">Resumo do Dia:</h3>
              <ul style="list-style: none; padding: 0;">
                <li><strong>Invoices Processadas:</strong> ${reportData.processedInvoices || 0}</li>
                <li><strong>Arquivos Gerados:</strong> ${reportData.totalFiles || 0}</li>
                <li><strong>Erros:</strong> ${reportData.errors || 0}</li>
                <li><strong>Taxa de Sucesso:</strong> ${reportData.successRate || '0%'}</li>
              </ul>
            </div>

            ${reportData.recentInvoices && reportData.recentInvoices.length > 0 ? `
              <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #155724;">Invoices Recentes:</h3>
                <ul>
                  ${reportData.recentInvoices.map(invoice => 
                    `<li>${invoice.invoiceNumber} - ${invoice.vendorName} - $${invoice.total}</li>`
                  ).join('')}
                </ul>
              </div>
            ` : ''}

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d;">
              <p>Este relatório foi gerado automaticamente pelo Sistema de Automação de Invoices.</p>
              <p>Data de geração: ${new Date().toLocaleString('pt-BR')}</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Gera texto simples para relatório diário
   */
  generateDailyReportText(reportData) {
    const date = new Date().toLocaleDateString('pt-BR');
    
    return `
RELATÓRIO DIÁRIO - ${date}

Resumo do Dia:
- Invoices Processadas: ${reportData.processedInvoices || 0}
- Arquivos Gerados: ${reportData.totalFiles || 0}
- Erros: ${reportData.errors || 0}
- Taxa de Sucesso: ${reportData.successRate || '0%'}

${reportData.recentInvoices && reportData.recentInvoices.length > 0 ? `
Invoices Recentes:
${reportData.recentInvoices.map(invoice => 
  `- ${invoice.invoiceNumber} - ${invoice.vendorName} - $${invoice.total}`
).join('\n')}
` : ''}

---
Este relatório foi gerado automaticamente pelo Sistema de Automação de Invoices.
Data de geração: ${new Date().toLocaleString('pt-BR')}
    `.trim();
  }

  /**
   * Prepara anexos para o e-mail
   */
  prepareAttachments(conversionResults) {
    const attachments = [];

    try {
      // Anexa arquivos CSV se existirem
      if (conversionResults.conversions.csv) {
        Object.entries(conversionResults.conversions.csv).forEach(([type, filePath]) => {
          attachments.push({
            filename: path.basename(filePath),
            path: filePath,
            contentType: 'text/csv'
          });
        });
      }

      // Anexa arquivos IIF se existirem
      if (conversionResults.conversions.iif) {
        Object.entries(conversionResults.conversions.iif).forEach(([type, filePath]) => {
          attachments.push({
            filename: path.basename(filePath),
            path: filePath,
            contentType: 'application/octet-stream'
          });
        });
      }
    } catch (error) {
      logError(error, { context: 'prepareAttachments', conversionResults });
    }

    return attachments;
  }

  /**
   * Testa a configuração de e-mail
   */
  async testEmailConfiguration() {
    try {
      if (!this.isConfigured) {
        throw new Error('Email notifier is not configured');
      }

      const testMailOptions = {
        from: config.notifications.email.smtp.auth.user,
        to: config.notifications.email.recipients[0], // Envia apenas para o primeiro destinatário
        subject: 'Teste - Sistema de Automação de Invoices',
        text: 'Este é um e-mail de teste para verificar a configuração do sistema.',
        html: '<p>Este é um e-mail de teste para verificar a configuração do sistema.</p>'
      };

      const result = await this.transporter.sendMail(testMailOptions);
      
      logger.info('Test email sent successfully', { messageId: result.messageId });
      return result;
    } catch (error) {
      logError(error, { context: 'testEmailConfiguration' });
      throw error;
    }
  }
}

module.exports = EmailNotifier;
