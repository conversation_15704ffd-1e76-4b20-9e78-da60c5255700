const fs = require('fs-extra');
const path = require('path');
const config = require('../config/config');
const { logger, logError, logProcessing } = require('../config/logger');

/**
 * Gerenciador de armazenamento local e em nuvem
 */
class StorageManager {
  constructor() {
    this.localPaths = {
      input: config.storage.inputPath,
      output: config.storage.outputPath,
      logs: config.storage.logsPath
    };
    
    this.cloudProviders = {
      googleDrive: null,
      oneDrive: null
    };

    this.initializeStorage();
  }

  /**
   * Inicializa diretórios de armazenamento local
   */
  async initializeStorage() {
    try {
      // Cria diretórios locais se não existirem
      for (const [type, dirPath] of Object.entries(this.localPaths)) {
        await fs.ensureDir(dirPath);
        logger.debug(`Storage directory ensured: ${type} -> ${dirPath}`);
      }

      // Inicializa provedores de nuvem se configurados
      if (config.storage.googleDrive.enabled) {
        await this.initializeGoogleDrive();
      }

      if (config.storage.oneDrive.enabled) {
        await this.initializeOneDrive();
      }

      logger.info('Storage manager initialized successfully');
    } catch (error) {
      logError(error, { context: 'initializeStorage' });
      throw error;
    }
  }

  /**
   * Salva um arquivo localmente
   */
  async saveFile(filePath, content, type = 'output') {
    try {
      const targetDir = this.localPaths[type] || this.localPaths.output;
      const fileName = path.basename(filePath);
      const fullPath = path.join(targetDir, fileName);

      await fs.ensureDir(targetDir);

      if (Buffer.isBuffer(content)) {
        await fs.writeFile(fullPath, content);
      } else if (typeof content === 'object') {
        await fs.writeJson(fullPath, content, { spaces: 2 });
      } else {
        await fs.writeFile(fullPath, content, 'utf8');
      }

      logProcessing('file_saved_locally', {
        originalPath: filePath,
        savedPath: fullPath,
        type,
        size: Buffer.isBuffer(content) ? content.length : content.toString().length
      });

      return fullPath;
    } catch (error) {
      logError(error, { context: 'saveFile', filePath, type });
      throw error;
    }
  }

  /**
   * Move arquivo para diretório de arquivo processado
   */
  async archiveFile(filePath) {
    try {
      const archiveDir = path.join(config.storage.localPath, 'archive');
      await fs.ensureDir(archiveDir);

      const fileName = path.basename(filePath);
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const archivedFileName = `${timestamp}_${fileName}`;
      const archivedPath = path.join(archiveDir, archivedFileName);

      if (config.storage.keepOriginalFiles) {
        await fs.copy(filePath, archivedPath);
      } else {
        await fs.move(filePath, archivedPath);
      }

      logProcessing('file_archived', {
        originalPath: filePath,
        archivedPath,
        keepOriginal: config.storage.keepOriginalFiles
      });

      return archivedPath;
    } catch (error) {
      logError(error, { context: 'archiveFile', filePath });
      throw error;
    }
  }

  /**
   * Lista arquivos em um diretório
   */
  async listFiles(type = 'output', extension = null) {
    try {
      const dirPath = this.localPaths[type] || this.localPaths.output;
      const files = await fs.readdir(dirPath);

      let filteredFiles = files.filter(file => {
        const filePath = path.join(dirPath, file);
        return fs.statSync(filePath).isFile();
      });

      if (extension) {
        filteredFiles = filteredFiles.filter(file => 
          path.extname(file).toLowerCase() === extension.toLowerCase()
        );
      }

      return filteredFiles.map(file => ({
        name: file,
        path: path.join(dirPath, file),
        stats: fs.statSync(path.join(dirPath, file))
      }));
    } catch (error) {
      logError(error, { context: 'listFiles', type, extension });
      throw error;
    }
  }

  /**
   * Limpa arquivos antigos baseado na configuração de retenção
   */
  async cleanupOldFiles() {
    try {
      const retentionDays = config.backup.logRetentionDays;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      let totalCleaned = 0;

      for (const [type, dirPath] of Object.entries(this.localPaths)) {
        const files = await this.listFiles(type);
        
        for (const file of files) {
          if (file.stats.mtime < cutoffDate) {
            await fs.remove(file.path);
            totalCleaned++;
            
            logProcessing('old_file_cleaned', {
              filePath: file.path,
              type,
              age: Math.floor((Date.now() - file.stats.mtime.getTime()) / (1000 * 60 * 60 * 24))
            });
          }
        }
      }

      logger.info(`Cleanup completed: ${totalCleaned} files removed`);
      return totalCleaned;
    } catch (error) {
      logError(error, { context: 'cleanupOldFiles' });
      throw error;
    }
  }

  /**
   * Inicializa Google Drive (placeholder)
   */
  async initializeGoogleDrive() {
    try {
      // TODO: Implementar integração com Google Drive
      logger.info('Google Drive integration would be initialized here');
      
      // const { google } = require('googleapis');
      // const drive = google.drive({ version: 'v3', auth: oauth2Client });
      // this.cloudProviders.googleDrive = drive;
      
    } catch (error) {
      logError(error, { context: 'initializeGoogleDrive' });
    }
  }

  /**
   * Inicializa OneDrive (placeholder)
   */
  async initializeOneDrive() {
    try {
      // TODO: Implementar integração com OneDrive
      logger.info('OneDrive integration would be initialized here');
      
    } catch (error) {
      logError(error, { context: 'initializeOneDrive' });
    }
  }

  /**
   * Faz upload para Google Drive
   */
  async uploadToGoogleDrive(filePath, fileName = null) {
    try {
      if (!config.storage.googleDrive.enabled) {
        throw new Error('Google Drive is not enabled');
      }

      // TODO: Implementar upload real
      logProcessing('google_drive_upload_simulated', {
        filePath,
        fileName: fileName || path.basename(filePath)
      });

      return `https://drive.google.com/file/d/simulated_file_id`;
    } catch (error) {
      logError(error, { context: 'uploadToGoogleDrive', filePath });
      throw error;
    }
  }

  /**
   * Faz upload para OneDrive
   */
  async uploadToOneDrive(filePath, fileName = null) {
    try {
      if (!config.storage.oneDrive.enabled) {
        throw new Error('OneDrive is not enabled');
      }

      // TODO: Implementar upload real
      logProcessing('onedrive_upload_simulated', {
        filePath,
        fileName: fileName || path.basename(filePath)
      });

      return `https://onedrive.live.com/simulated_file_id`;
    } catch (error) {
      logError(error, { context: 'uploadToOneDrive', filePath });
      throw error;
    }
  }

  /**
   * Faz backup de arquivos para nuvem
   */
  async backupToCloud(filePaths) {
    const results = {
      googleDrive: [],
      oneDrive: [],
      errors: []
    };

    for (const filePath of filePaths) {
      try {
        // Upload para Google Drive se habilitado
        if (config.storage.googleDrive.enabled) {
          const driveUrl = await this.uploadToGoogleDrive(filePath);
          results.googleDrive.push({ filePath, url: driveUrl });
        }

        // Upload para OneDrive se habilitado
        if (config.storage.oneDrive.enabled) {
          const onedriveUrl = await this.uploadToOneDrive(filePath);
          results.oneDrive.push({ filePath, url: onedriveUrl });
        }
      } catch (error) {
        logError(error, { context: 'backupToCloud', filePath });
        results.errors.push({ filePath, error: error.message });
      }
    }

    logProcessing('cloud_backup_completed', {
      totalFiles: filePaths.length,
      googleDriveUploads: results.googleDrive.length,
      onedriveUploads: results.oneDrive.length,
      errors: results.errors.length
    });

    return results;
  }

  /**
   * Obtém estatísticas de armazenamento
   */
  async getStorageStats() {
    const stats = {
      local: {},
      cloud: {
        googleDrive: { enabled: config.storage.googleDrive.enabled },
        oneDrive: { enabled: config.storage.oneDrive.enabled }
      }
    };

    try {
      for (const [type, dirPath] of Object.entries(this.localPaths)) {
        const files = await this.listFiles(type);
        const totalSize = files.reduce((sum, file) => sum + file.stats.size, 0);
        
        stats.local[type] = {
          path: dirPath,
          fileCount: files.length,
          totalSize: totalSize,
          totalSizeMB: Math.round(totalSize / (1024 * 1024) * 100) / 100
        };
      }
    } catch (error) {
      logError(error, { context: 'getStorageStats' });
    }

    return stats;
  }

  /**
   * Cria backup completo do sistema
   */
  async createSystemBackup() {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupDir = path.join(config.storage.localPath, 'backups', timestamp);
      
      await fs.ensureDir(backupDir);

      // Copia todos os arquivos importantes
      for (const [type, dirPath] of Object.entries(this.localPaths)) {
        const targetDir = path.join(backupDir, type);
        await fs.copy(dirPath, targetDir);
      }

      // Cria arquivo de metadados do backup
      const backupMetadata = {
        timestamp: new Date().toISOString(),
        version: config.app.version,
        directories: this.localPaths,
        stats: await this.getStorageStats()
      };

      await fs.writeJson(path.join(backupDir, 'backup_metadata.json'), backupMetadata, { spaces: 2 });

      logProcessing('system_backup_created', {
        backupDir,
        timestamp
      });

      return backupDir;
    } catch (error) {
      logError(error, { context: 'createSystemBackup' });
      throw error;
    }
  }
}

module.exports = StorageManager;
