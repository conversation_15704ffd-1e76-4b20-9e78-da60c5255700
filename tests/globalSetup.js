const fs = require('fs-extra');
const path = require('path');

module.exports = async () => {
  console.log('Setting up global test environment...');
  
  // Create global test directories
  const globalTestDir = path.join(__dirname, '../test-data');
  await fs.ensureDir(globalTestDir);
  await fs.ensureDir(path.join(globalTestDir, 'input'));
  await fs.ensureDir(path.join(globalTestDir, 'output'));
  await fs.ensureDir(path.join(globalTestDir, 'logs'));
  
  // Create test fixtures
  await createTestFixtures(globalTestDir);
  
  // Set global environment variables for all tests
  process.env.NODE_ENV = 'test';
  process.env.JEST_WORKER_ID = process.env.JEST_WORKER_ID || '1';
  
  console.log('Global test environment setup completed');
};

async function createTestFixtures(testDir) {
  const fixturesDir = path.join(testDir, 'fixtures');
  await fs.ensureDir(fixturesDir);
  
  // Create sample PDF content (mock)
  const samplePdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Invoice #12345) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF`;

  await fs.writeFile(path.join(fixturesDir, 'sample-invoice.pdf'), samplePdfContent);
  
  // Create sample Excel content (CSV format for simplicity)
  const sampleExcelContent = `Invoice Number,Date,Vendor,Amount,Description
INV-001,2024-01-15,Test Vendor,100.50,Test invoice description
INV-002,2024-01-16,Another Vendor,250.75,Another test invoice`;

  await fs.writeFile(path.join(fixturesDir, 'sample-invoice.csv'), sampleExcelContent);
  
  // Create sample configuration file
  const sampleConfig = {
    zohoMail: {
      enabled: false,
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      checkInterval: 5,
      emailFilter: {
        senderEmail: '<EMAIL>',
        attachmentKeyword: 'test'
      }
    },
    notifications: {
      email: {
        enabled: false,
        recipients: ['<EMAIL>']
      },
      slack: {
        enabled: false,
        webhookUrl: 'https://hooks.slack.com/test'
      }
    },
    quickbooks: {
      defaultOutputFormat: 'csv',
      defaultVendorName: 'Test Vendor',
      defaultExpenseCategory: 'Test Category'
    }
  };
  
  await fs.writeFile(
    path.join(fixturesDir, 'test-config.json'),
    JSON.stringify(sampleConfig, null, 2)
  );
  
  console.log('Test fixtures created successfully');
}
