const { describe, it, expect, beforeEach } = require('@jest/globals');
const config = require('../../src/config/config');

describe('Configuration', () => {
  describe('App Configuration', () => {
    it('should have required app properties', () => {
      expect(config.app).toBeDefined();
      expect(config.app.name).toBe('invoice-automation-system');
      expect(config.app.version).toBeDefined();
      expect(config.app.env).toBeDefined();
    });

    it('should have valid environment', () => {
      expect(['development', 'production', 'test']).toContain(config.app.env);
    });
  });

  describe('Zoho Mail Configuration', () => {
    it('should have zoho mail configuration', () => {
      expect(config.zohoMail).toBeDefined();
      expect(config.zohoMail.enabled).toBeDefined();
      expect(config.zohoMail.checkInterval).toBeGreaterThan(0);
      expect(config.zohoMail.emailFilter).toBeDefined();
    });

    it('should have email filter configuration', () => {
      const filter = config.zohoMail.emailFilter;
      expect(filter.senderEmail).toBeDefined();
      expect(filter.attachmentKeyword).toBeDefined();
      expect(Array.isArray(filter.allowedExtensions)).toBe(true);
    });

    it('should have valid check interval', () => {
      expect(config.zohoMail.checkInterval).toBeGreaterThanOrEqual(1);
      expect(config.zohoMail.checkInterval).toBeLessThanOrEqual(60);
    });
  });

  describe('QuickBooks Configuration', () => {
    it('should have quickbooks configuration', () => {
      expect(config.quickbooks).toBeDefined();
      expect(config.quickbooks.defaultOutputFormat).toBeDefined();
      expect(config.quickbooks.defaultVendorName).toBeDefined();
      expect(config.quickbooks.defaultExpenseCategory).toBeDefined();
    });

    it('should have valid output format', () => {
      const validFormats = ['csv', 'iif', 'both'];
      expect(validFormats).toContain(config.quickbooks.defaultOutputFormat);
    });
  });

  describe('Storage Configuration', () => {
    it('should have storage configuration', () => {
      expect(config.storage).toBeDefined();
      expect(config.storage.localPath).toBeDefined();
      expect(config.storage.inputPath).toBeDefined();
      expect(config.storage.outputPath).toBeDefined();
      expect(config.storage.logsPath).toBeDefined();
    });

    it('should have boolean flags', () => {
      expect(typeof config.storage.keepOriginalFiles).toBe('boolean');
      expect(typeof config.storage.googleDrive.enabled).toBe('boolean');
      expect(typeof config.storage.oneDrive.enabled).toBe('boolean');
    });
  });

  describe('Notifications Configuration', () => {
    it('should have notifications configuration', () => {
      expect(config.notifications).toBeDefined();
      expect(config.notifications.email).toBeDefined();
      expect(config.notifications.slack).toBeDefined();
    });

    it('should have email notification config', () => {
      const email = config.notifications.email;
      expect(typeof email.enabled).toBe('boolean');
      expect(Array.isArray(email.recipients)).toBe(true);
      expect(email.smtp).toBeDefined();
      expect(email.smtp.host).toBeDefined();
      expect(email.smtp.port).toBeGreaterThan(0);
    });

    it('should have slack notification config', () => {
      const slack = config.notifications.slack;
      expect(typeof slack.enabled).toBe('boolean');
    });
  });

  describe('Web Configuration', () => {
    it('should have web configuration', () => {
      expect(config.web).toBeDefined();
      expect(typeof config.web.enabled).toBe('boolean');
      expect(config.web.port).toBeGreaterThan(0);
      expect(config.web.port).toBeLessThan(65536);
    });
  });

  describe('Backup Configuration', () => {
    it('should have backup configuration', () => {
      expect(config.backup).toBeDefined();
      expect(config.backup.logRetentionDays).toBeGreaterThan(0);
    });
  });

  describe('Configuration Validation', () => {
    it('should validate working hours', () => {
      const isWorkingHours = config.isWorkingHours();
      expect(typeof isWorkingHours).toBe('boolean');
    });

    it('should validate email format', () => {
      expect(config.validateEmail('<EMAIL>')).toBe(true);
      expect(config.validateEmail('invalid-email')).toBe(false);
      expect(config.validateEmail('')).toBe(false);
      expect(config.validateEmail(null)).toBe(false);
    });

    it('should validate required environment variables', () => {
      const validation = config.validateRequiredEnvVars();
      expect(Array.isArray(validation.missing)).toBe(true);
      expect(Array.isArray(validation.invalid)).toBe(true);
    });
  });

  describe('Environment Variables', () => {
    it('should handle missing environment variables gracefully', () => {
      // Test that config doesn't throw when env vars are missing
      expect(() => {
        const testConfig = require('../../src/config/config');
      }).not.toThrow();
    });

    it('should use default values when env vars are not set', () => {
      expect(config.zohoMail.checkInterval).toBeDefined();
      expect(config.web.port).toBeDefined();
      expect(config.quickbooks.defaultOutputFormat).toBeDefined();
    });
  });

  describe('Path Resolution', () => {
    it('should resolve storage paths correctly', () => {
      expect(config.storage.inputPath).toContain('input');
      expect(config.storage.outputPath).toContain('output');
      expect(config.storage.logsPath).toContain('logs');
    });

    it('should have absolute paths', () => {
      const path = require('path');
      expect(path.isAbsolute(config.storage.localPath)).toBe(true);
      expect(path.isAbsolute(config.storage.inputPath)).toBe(true);
      expect(path.isAbsolute(config.storage.outputPath)).toBe(true);
      expect(path.isAbsolute(config.storage.logsPath)).toBe(true);
    });
  });
});
