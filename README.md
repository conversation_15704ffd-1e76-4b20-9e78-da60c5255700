# Sistema de Automação de Invoices - Zoho Mail para QuickBooks

## 📋 Visão Geral

Sistema automatizado que monitora e-mails do Zoho Mail, identifica invoices da "Angus", extrai dados e converte para formatos compatíveis com QuickBooks Online e Desktop.

## 🔄 Fluxo de Automação

1. **Monitoramento**: Monitora e-mails do Zoho Mail com filtros específicos
2. **Identificação**: Detecta anexos contendo "angus" no nome
3. **Extração**: Processa PDFs (OCR) e arquivos Excel para extrair dados
4. **Conversão**: Gera arquivos CSV (QBO) ou IIF (Desktop) 
5. **Armazenamento**: Salva arquivos e envia notificações

## 🏗️ Estrutura do Projeto

```
├── src/
│   ├── email/              # Módulos de e-mail
│   ├── extractors/         # Extratores de dados (PDF/Excel)
│   ├── converters/         # Conversores para QuickBooks
│   ├── storage/            # Sistema de armazenamento
│   ├── notifications/      # Sistema de notificações
│   └── config/             # Configurações
├── tests/                  # Testes unitários e integração
├── web/                    # Interface web de configuração
├── data/                   # Dados processados
│   ├── input/              # Arquivos de entrada
│   ├── output/             # Arquivos convertidos
│   └── logs/               # Logs do sistema
└── docs/                   # Documentação

```

## ⚙️ Configuração

### Credenciais Necessárias:
- **Zoho Mail**: Client ID, Client Secret, Refresh Token
- **Google Drive/OneDrive**: Para armazenamento (opcional)
- **Slack/E-mail**: Para notificações

### Filtros de E-mail:
- **Remetente**: <EMAIL>
- **Anexo contém**: "angus"
- **Tipos suportados**: PDF, XLS, XLSX

## 🚀 Instalação e Uso

```bash
# Instalar dependências
npm install

# Configurar credenciais
cp .env.example .env
# Editar .env com suas credenciais

# Iniciar monitoramento
npm start

# Interface web (configuração)
npm run web
```

## 📊 Formatos de Saída

### QuickBooks Online (CSV)
```csv
Vendor,Date,Category,Description,Amount
KeepGreen Irrigation,2025-06-30,Repairs and Maintenance,Angus Sprinkler Repair,250.00
```

### QuickBooks Desktop (IIF)
```
!TRNS	TRNSID	TRNSTYPE	DATE	ACCNT	NAME	CLASS	AMOUNT	DOCNUM	MEMO	CLEAR	TOPRINT	NAMEADDRLIST	ADDR1	ADDR2	ADDR3	ADDR4	ADDR5	DUEDATE	TERMS	PAID	SHIPDATE
!SPL	SPLID	TRNSTYPE	DATE	ACCNT	NAME	CLASS	AMOUNT	DOCNUM	MEMO	CLEAR	QNTY	PRICE	INVITEM	PAYMETH	TAXABLE	REIMBEXP	SERVICEDATE	OTHER2
!ENDTRNS
TRNS		BILL	6/30/2025	Accounts Payable	KeepGreen Irrigation		250.00	INV-001	Angus Sprinkler Repair	N	N
SPL		BILL	6/30/2025	Repairs and Maintenance	KeepGreen Irrigation		-250.00	INV-001	Angus Sprinkler Repair	N
ENDTRNS
```

## 🔧 Tecnologias Utilizadas

- **Node.js**: Runtime principal
- **Express**: Interface web
- **PDF-Parse**: Extração de dados de PDF
- **ExcelJS**: Processamento de arquivos Excel
- **Tesseract.js**: OCR para PDFs escaneados
- **Nodemailer**: Envio de notificações
- **Winston**: Sistema de logs

## 📝 Logs e Monitoramento

O sistema gera logs detalhados em `data/logs/` incluindo:
- Processamento de e-mails
- Extração de dados
- Conversões realizadas
- Erros e exceções

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request
