const CSVConverter = require('./csvConverter');
const IIFConverter = require('./iifConverter');
const config = require('../config/config');
const { logger, logError, logProcessing } = require('../config/logger');
const path = require('path');

/**
 * Conversor principal que coordena a conversão para diferentes formatos do QuickBooks
 */
class QuickBooksConverter {
  constructor() {
    this.csvConverter = new CSVConverter();
    this.iifConverter = new IIFConverter();
  }

  /**
   * Converte dados de invoice para formato QuickBooks
   */
  async convertToQuickBooks(invoiceData, options = {}) {
    try {
      logProcessing('quickbooks_conversion_started', {
        invoiceNumber: invoiceData.invoiceNumber,
        vendorName: invoiceData.vendorName,
        total: invoiceData.total,
        options
      });

      // Valida dados de entrada
      const validationErrors = this.validateInvoiceData(invoiceData);
      if (validationErrors.length > 0) {
        throw new Error(`Validation errors: ${validationErrors.join(', ')}`);
      }

      const results = {
        invoiceData: invoiceData,
        conversions: {},
        summary: {
          totalFiles: 0,
          successfulConversions: 0,
          errors: []
        }
      };

      // Determina quais formatos gerar baseado na configuração e opções
      const formatsToGenerate = this.determineFormatsToGenerate(options);

      // Gera conversões CSV (QuickBooks Online)
      if (formatsToGenerate.includes('csv')) {
        try {
          const csvResults = await this.csvConverter.generateAllCSVFormats(invoiceData);
          results.conversions.csv = csvResults;
          results.summary.totalFiles += Object.keys(csvResults).length;
          results.summary.successfulConversions += Object.keys(csvResults).length;
        } catch (error) {
          logError(error, { context: 'CSV conversion', invoiceData });
          results.summary.errors.push(`CSV conversion failed: ${error.message}`);
        }
      }

      // Gera conversões IIF (QuickBooks Desktop)
      if (formatsToGenerate.includes('iif')) {
        try {
          const iifResults = await this.iifConverter.generateAllIIFFormats(invoiceData);
          results.conversions.iif = iifResults;
          results.summary.totalFiles += Object.keys(iifResults).length;
          results.summary.successfulConversions += Object.keys(iifResults).length;
        } catch (error) {
          logError(error, { context: 'IIF conversion', invoiceData });
          results.summary.errors.push(`IIF conversion failed: ${error.message}`);
        }
      }

      // Gera arquivo de resumo
      await this.generateSummaryFile(results);

      logProcessing('quickbooks_conversion_completed', {
        invoiceNumber: invoiceData.invoiceNumber,
        totalFiles: results.summary.totalFiles,
        successfulConversions: results.summary.successfulConversions,
        errors: results.summary.errors.length
      });

      // Dispara notificações se configurado
      await this.triggerNotifications(results);

      return results;
    } catch (error) {
      logError(error, { context: 'convertToQuickBooks', invoiceData, options });
      throw error;
    }
  }

  /**
   * Determina quais formatos gerar baseado na configuração e opções
   */
  determineFormatsToGenerate(options) {
    const formats = [];

    // Verifica configuração padrão
    const defaultFormat = config.quickbooks.defaultOutputFormat;
    
    if (options.formats) {
      // Usa formatos especificados nas opções
      formats.push(...options.formats);
    } else if (options.format) {
      // Usa formato único especificado
      formats.push(options.format);
    } else {
      // Usa configuração padrão
      if (defaultFormat === 'csv') {
        formats.push('csv');
      } else if (defaultFormat === 'iif') {
        formats.push('iif');
      } else {
        // Se não especificado, gera ambos
        formats.push('csv', 'iif');
      }
    }

    return [...new Set(formats)]; // Remove duplicatas
  }

  /**
   * Valida dados de invoice antes da conversão
   */
  validateInvoiceData(invoiceData) {
    const errors = [];

    // Validações básicas
    if (!invoiceData) {
      errors.push('Invoice data is required');
      return errors;
    }

    // Valida fornecedor
    if (!invoiceData.vendorName && !config.quickbooks.defaultVendorName) {
      errors.push('Vendor name is required');
    }

    // Valida valor total ou itens de linha
    const hasTotal = invoiceData.total && !isNaN(parseFloat(invoiceData.total)) && parseFloat(invoiceData.total) > 0;
    const hasLineItems = invoiceData.lineItems && invoiceData.lineItems.length > 0;
    
    if (!hasTotal && !hasLineItems) {
      errors.push('Total amount or line items are required');
    }

    // Valida data
    if (invoiceData.invoiceDate) {
      const date = new Date(invoiceData.invoiceDate);
      if (isNaN(date.getTime())) {
        errors.push('Invalid invoice date format');
      }
    }

    // Valida itens de linha se existirem
    if (hasLineItems) {
      invoiceData.lineItems.forEach((item, index) => {
        if (!item.description || item.description.trim().length === 0) {
          errors.push(`Line item ${index + 1}: Description is required`);
        }
        
        if (item.amount && isNaN(parseFloat(item.amount))) {
          errors.push(`Line item ${index + 1}: Invalid amount format`);
        }
      });
    }

    return errors;
  }

  /**
   * Gera arquivo de resumo da conversão
   */
  async generateSummaryFile(results) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const summaryFileName = `conversion_summary_${results.invoiceData.invoiceNumber || timestamp}.json`;
      const summaryPath = path.join(config.storage.outputPath, summaryFileName);

      const summaryData = {
        conversionDate: new Date().toISOString(),
        invoiceData: {
          invoiceNumber: results.invoiceData.invoiceNumber,
          vendorName: results.invoiceData.vendorName,
          invoiceDate: results.invoiceData.invoiceDate,
          total: results.invoiceData.total,
          lineItemsCount: results.invoiceData.lineItems ? results.invoiceData.lineItems.length : 0
        },
        generatedFiles: {},
        summary: results.summary
      };

      // Lista arquivos gerados
      if (results.conversions.csv) {
        summaryData.generatedFiles.csv = Object.values(results.conversions.csv).map(filePath => path.basename(filePath));
      }

      if (results.conversions.iif) {
        summaryData.generatedFiles.iif = Object.values(results.conversions.iif).map(filePath => path.basename(filePath));
      }

      const fs = require('fs-extra');
      await fs.writeJson(summaryPath, summaryData, { spaces: 2 });

      logProcessing('summary_file_generated', {
        summaryPath,
        invoiceNumber: results.invoiceData.invoiceNumber
      });

    } catch (error) {
      logError(error, { context: 'generateSummaryFile', results });
    }
  }

  /**
   * Dispara notificações sobre a conversão
   */
  async triggerNotifications(results) {
    try {
      if (config.notifications.email.enabled || config.notifications.slack.enabled) {
        // TODO: Integrar com sistema de notificações
        logProcessing('notifications_triggered', {
          invoiceNumber: results.invoiceData.invoiceNumber,
          totalFiles: results.summary.totalFiles,
          errors: results.summary.errors.length
        });

        // const notificationService = require('../notifications/notificationService');
        // await notificationService.sendConversionNotification(results);
      }
    } catch (error) {
      logError(error, { context: 'triggerNotifications', results });
    }
  }

  /**
   * Converte múltiplas invoices em lote
   */
  async convertBatch(invoicesData, options = {}) {
    const batchResults = {
      totalInvoices: invoicesData.length,
      successfulConversions: 0,
      failedConversions: 0,
      results: [],
      errors: []
    };

    logProcessing('batch_conversion_started', {
      totalInvoices: invoicesData.length,
      options
    });

    for (let i = 0; i < invoicesData.length; i++) {
      const invoiceData = invoicesData[i];
      
      try {
        const result = await this.convertToQuickBooks(invoiceData, options);
        batchResults.results.push({
          index: i,
          invoiceNumber: invoiceData.invoiceNumber,
          success: true,
          result
        });
        batchResults.successfulConversions++;
      } catch (error) {
        logError(error, { context: 'batch conversion', index: i, invoiceData });
        batchResults.results.push({
          index: i,
          invoiceNumber: invoiceData.invoiceNumber,
          success: false,
          error: error.message
        });
        batchResults.failedConversions++;
        batchResults.errors.push({
          index: i,
          invoiceNumber: invoiceData.invoiceNumber,
          error: error.message
        });
      }
    }

    logProcessing('batch_conversion_completed', batchResults);

    return batchResults;
  }

  /**
   * Obtém estatísticas de conversões
   */
  getConversionStats() {
    // TODO: Implementar estatísticas baseadas em logs
    return {
      totalConversions: 0,
      successfulConversions: 0,
      failedConversions: 0,
      averageProcessingTime: 0
    };
  }
}

module.exports = QuickBooksConverter;
