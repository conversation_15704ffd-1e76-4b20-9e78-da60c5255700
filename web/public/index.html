<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Automação de Invoices</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-file-invoice-dollar me-2"></i>
                Sistema de Automação de Invoices
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <span id="system-status" class="badge bg-secondary">Carregando...</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Alertas -->
        <div id="alerts-container"></div>

        <!-- Dashboard Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Invoices Hoje</h5>
                                <h2 id="invoices-today">0</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-file-invoice fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Arquivos Gerados</h5>
                                <h2 id="files-generated">0</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-file-export fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Erros</h5>
                                <h2 id="errors-count">0</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-primary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Uptime</h5>
                                <h6 id="system-uptime">0h 0m</h6>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="monitor-tab" data-bs-toggle="tab" data-bs-target="#monitor" type="button" role="tab">
                    <i class="fas fa-envelope me-2"></i>Monitor de E-mail
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="files-tab" data-bs-toggle="tab" data-bs-target="#files" type="button" role="tab">
                    <i class="fas fa-folder me-2"></i>Arquivos
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="config-tab" data-bs-toggle="tab" data-bs-target="#config" type="button" role="tab">
                    <i class="fas fa-cog me-2"></i>Configuração
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">
                    <i class="fas fa-list me-2"></i>Logs
                </button>
            </li>
        </ul>

        <div class="tab-content mt-3" id="mainTabsContent">
            <!-- Dashboard Tab -->
            <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Status dos Serviços</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="service-status">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span>Monitor de E-mail</span>
                                                <span id="email-monitor-status" class="badge bg-secondary">Verificando...</span>
                                            </div>
                                            <div class="d-flex gap-2">
                                                <button id="start-monitor-btn" class="btn btn-sm btn-success">Iniciar</button>
                                                <button id="stop-monitor-btn" class="btn btn-sm btn-danger">Parar</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="service-status">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span>Notificações</span>
                                                <span id="notifications-status" class="badge bg-secondary">Verificando...</span>
                                            </div>
                                            <button id="test-notifications-btn" class="btn btn-sm btn-primary">Testar</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Ações Rápidas</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="refreshStatus()">
                                        <i class="fas fa-sync-alt me-2"></i>Atualizar Status
                                    </button>
                                    <button class="btn btn-info" onclick="downloadLogs()">
                                        <i class="fas fa-download me-2"></i>Download Logs
                                    </button>
                                    <button class="btn btn-warning" onclick="clearCache()">
                                        <i class="fas fa-trash me-2"></i>Limpar Cache
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monitor Tab -->
            <div class="tab-pane fade" id="monitor" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Controle do Monitor de E-mail</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Status Atual</h6>
                                <p>Monitor: <span id="monitor-status-detail" class="badge bg-secondary">Verificando...</span></p>
                                <p>Última verificação: <span id="last-check">Nunca</span></p>
                                <p>Próxima verificação: <span id="next-check">-</span></p>
                            </div>
                            <div class="col-md-6">
                                <h6>Configuração</h6>
                                <p>Intervalo: <span id="check-interval">-</span></p>
                                <p>E-mail alvo: <span id="target-email">-</span></p>
                                <p>Palavra-chave: <span id="keyword-filter">-</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Files Tab -->
            <div class="tab-pane fade" id="files" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Arquivos Gerados</h5>
                        <button class="btn btn-sm btn-primary" onclick="refreshFiles()">
                            <i class="fas fa-sync-alt me-1"></i>Atualizar
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <select class="form-select" id="file-type-filter">
                                        <option value="">Todos os tipos</option>
                                        <option value=".csv">CSV</option>
                                        <option value=".iif">IIF</option>
                                        <option value=".json">JSON</option>
                                    </select>
                                </div>
                                <div class="col-md-8">
                                    <input type="text" class="form-control" id="file-search" placeholder="Buscar arquivos...">
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nome do Arquivo</th>
                                        <th>Tamanho</th>
                                        <th>Data de Criação</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody id="files-table-body">
                                    <tr>
                                        <td colspan="4" class="text-center">Carregando arquivos...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Config Tab -->
            <div class="tab-pane fade" id="config" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Configuração do Zoho Mail</h5>
                            </div>
                            <div class="card-body">
                                <form id="zoho-config-form">
                                    <div class="mb-3">
                                        <label class="form-label">Status</label>
                                        <div>
                                            <span id="zoho-status" class="badge bg-secondary">Verificando...</span>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Intervalo de Verificação (minutos)</label>
                                        <input type="number" class="form-control" id="check-interval-input" min="1" max="60">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">E-mail do Remetente</label>
                                        <input type="email" class="form-control" id="sender-email-input">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Palavra-chave no Anexo</label>
                                        <input type="text" class="form-control" id="attachment-keyword-input">
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Configuração de Notificações</h5>
                            </div>
                            <div class="card-body">
                                <form id="notifications-config-form">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="email-notifications-enabled">
                                            <label class="form-check-label" for="email-notifications-enabled">
                                                Notificações por E-mail
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Destinatários (separados por vírgula)</label>
                                        <textarea class="form-control" id="email-recipients" rows="2"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="slack-notifications-enabled">
                                            <label class="form-check-label" for="slack-notifications-enabled">
                                                Notificações via Slack
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Webhook URL do Slack</label>
                                        <input type="url" class="form-control" id="slack-webhook-url">
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Configuração do QuickBooks</h5>
                            </div>
                            <div class="card-body">
                                <form id="quickbooks-config-form">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Formato Padrão</label>
                                                <select class="form-select" id="default-format">
                                                    <option value="csv">CSV (QuickBooks Online)</option>
                                                    <option value="iif">IIF (QuickBooks Desktop)</option>
                                                    <option value="both">Ambos</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Fornecedor Padrão</label>
                                                <input type="text" class="form-control" id="default-vendor">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Categoria de Despesa Padrão</label>
                                                <input type="text" class="form-control" id="default-expense-category">
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <button class="btn btn-primary me-2" onclick="saveConfiguration()">
                            <i class="fas fa-save me-2"></i>Salvar Configuração
                        </button>
                        <button class="btn btn-secondary" onclick="loadConfiguration()">
                            <i class="fas fa-sync-alt me-2"></i>Recarregar
                        </button>
                    </div>
                </div>
            </div>

            <!-- Logs Tab -->
            <div class="tab-pane fade" id="logs" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Logs do Sistema</h5>
                        <div>
                            <select class="form-select form-select-sm me-2" id="log-type-select" style="width: auto; display: inline-block;">
                                <option value="combined">Todos</option>
                                <option value="email">E-mail</option>
                                <option value="conversion">Conversão</option>
                                <option value="error">Erros</option>
                            </select>
                            <button class="btn btn-sm btn-primary" onclick="refreshLogs()">
                                <i class="fas fa-sync-alt me-1"></i>Atualizar
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="logs-container">
                            <pre id="logs-content" class="logs-pre">Carregando logs...</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
