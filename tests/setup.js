const fs = require('fs-extra');
const path = require('path');

// Global test setup
beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';
  
  // Create test directories
  const testDirs = [
    path.join(__dirname, 'temp'),
    path.join(__dirname, 'temp/input'),
    path.join(__dirname, 'temp/output'),
    path.join(__dirname, 'temp/logs'),
    path.join(__dirname, 'fixtures')
  ];
  
  for (const dir of testDirs) {
    await fs.ensureDir(dir);
  }
  
  // Set test environment variables
  process.env.STORAGE_LOCAL_PATH = path.join(__dirname, 'temp');
  process.env.WEB_PORT = '3001';
  process.env.ZOHO_AUTO_START = 'false';
  process.env.WEB_ENABLED = 'false';
  process.env.EMAIL_NOTIFICATIONS_ENABLED = 'false';
  process.env.SLACK_NOTIFICATIONS_ENABLED = 'false';
  
  console.log('Test environment setup completed');
});

// Global test teardown
afterAll(async () => {
  // Cleanup test directories
  try {
    await fs.remove(path.join(__dirname, 'temp'));
    console.log('Test environment cleanup completed');
  } catch (error) {
    console.warn('Error during test cleanup:', error.message);
  }
});

// Mock console methods to reduce noise in tests
const originalConsole = { ...console };

beforeEach(() => {
  // Suppress console output in tests unless explicitly needed
  if (!process.env.JEST_VERBOSE) {
    console.log = jest.fn();
    console.info = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
  }
});

afterEach(() => {
  // Restore console methods
  if (!process.env.JEST_VERBOSE) {
    console.log = originalConsole.log;
    console.info = originalConsole.info;
    console.warn = originalConsole.warn;
    console.error = originalConsole.error;
  }
  
  // Clear all mocks
  jest.clearAllMocks();
});

// Global test utilities
global.testUtils = {
  // Create a mock invoice data
  createMockInvoice: (overrides = {}) => ({
    invoiceNumber: 'TEST-001',
    date: '2024-01-15',
    vendor: 'Test Vendor',
    amount: 100.50,
    description: 'Test invoice',
    lineItems: [
      {
        description: 'Test item',
        amount: 100.50,
        category: 'Test Category'
      }
    ],
    ...overrides
  }),
  
  // Create a mock email data
  createMockEmail: (overrides = {}) => ({
    id: 'test-email-id',
    subject: 'Test Invoice Email',
    from: '<EMAIL>',
    date: new Date().toISOString(),
    attachments: [
      {
        filename: 'angus-invoice-001.pdf',
        contentType: 'application/pdf',
        size: 1024
      }
    ],
    ...overrides
  }),
  
  // Create test file
  createTestFile: async (filename, content = 'test content') => {
    const filePath = path.join(__dirname, 'temp', filename);
    await fs.writeFile(filePath, content);
    return filePath;
  },
  
  // Wait for a specified time
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Get test data directory
  getTestDataDir: () => path.join(__dirname, 'temp'),
  
  // Get fixtures directory
  getFixturesDir: () => path.join(__dirname, 'fixtures')
};

// Mock external dependencies that require credentials
jest.mock('nodemailer', () => ({
  createTransporter: jest.fn(() => ({
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
    verify: jest.fn().mockResolvedValue(true)
  }))
}));

// Mock Axios for HTTP requests
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn().mockResolvedValue({ data: {} }),
    post: jest.fn().mockResolvedValue({ data: {} }),
    put: jest.fn().mockResolvedValue({ data: {} }),
    delete: jest.fn().mockResolvedValue({ data: {} })
  })),
  get: jest.fn().mockResolvedValue({ data: {} }),
  post: jest.fn().mockResolvedValue({ data: {} })
}));

// Mock file system operations that might fail in test environment
const originalFs = jest.requireActual('fs-extra');
jest.mock('fs-extra', () => ({
  ...originalFs,
  // Override specific methods if needed for testing
}));

// Error handling for unhandled promises in tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process in tests, just log the error
});

// Increase timeout for integration tests
jest.setTimeout(30000);
