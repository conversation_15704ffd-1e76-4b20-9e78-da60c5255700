const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const config = require('../src/config/config');
const { logger, logError, logProcessing } = require('../src/config/logger');

// Importa serviços
const EmailMonitor = require('../src/email/emailMonitor');
const QuickBooksConverter = require('../src/converters/quickbooksConverter');
const NotificationService = require('../src/notifications/notificationService');
const StorageManager = require('../src/storage/storageManager');

/**
 * Servidor web para interface de configuração e monitoramento
 */
class WebServer {
  constructor() {
    this.app = express();
    this.port = config.web.port || 3000;
    
    // Inicializa serviços
    this.emailMonitor = new EmailMonitor();
    this.quickbooksConverter = new QuickBooksConverter();
    this.notificationService = new NotificationService();
    this.storageManager = new StorageManager();
    
    this.setupMiddleware();
    this.setupRoutes();
  }

  /**
   * Configura middleware do Express
   */
  setupMiddleware() {
    // Parse JSON
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));

    // Serve arquivos estáticos
    this.app.use(express.static(path.join(__dirname, 'public')));

    // CORS para desenvolvimento
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      next();
    });

    // Log de requisições
    this.app.use((req, res, next) => {
      logger.debug(`${req.method} ${req.path}`, { 
        ip: req.ip, 
        userAgent: req.get('User-Agent') 
      });
      next();
    });
  }

  /**
   * Configura rotas da API
   */
  setupRoutes() {
    // Página principal
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });

    // API Routes
    this.app.use('/api', this.createApiRoutes());

    // Rota de health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: config.app.version,
        uptime: process.uptime()
      });
    });

    // Handler de erro 404
    this.app.use('*', (req, res) => {
      res.status(404).json({ error: 'Route not found' });
    });

    // Handler de erros
    this.app.use((error, req, res, next) => {
      logError(error, { context: 'web server', path: req.path });
      res.status(500).json({ 
        error: 'Internal server error',
        message: config.app.env === 'development' ? error.message : 'Something went wrong'
      });
    });
  }

  /**
   * Cria rotas da API
   */
  createApiRoutes() {
    const router = express.Router();

    // Status do sistema
    router.get('/status', async (req, res) => {
      try {
        const status = {
          system: {
            status: 'running',
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: config.app.version
          },
          services: {
            emailMonitor: this.emailMonitor.isRunning(),
            notifications: this.notificationService.getNotificationStats(),
            storage: await this.storageManager.getStorageStats()
          },
          configuration: {
            zohoMail: {
              configured: !!(config.zohoMail.clientId && config.zohoMail.clientSecret),
              enabled: config.zohoMail.enabled
            },
            quickbooks: {
              defaultFormat: config.quickbooks.defaultOutputFormat,
              defaultVendor: config.quickbooks.defaultVendorName
            }
          }
        };

        res.json(status);
      } catch (error) {
        logError(error, { context: 'status endpoint' });
        res.status(500).json({ error: error.message });
      }
    });

    // Configuração
    router.get('/config', (req, res) => {
      try {
        // Retorna configuração sem dados sensíveis
        const safeConfig = {
          app: config.app,
          zohoMail: {
            enabled: config.zohoMail.enabled,
            checkInterval: config.zohoMail.checkInterval,
            emailFilter: config.zohoMail.emailFilter
          },
          quickbooks: config.quickbooks,
          notifications: {
            email: {
              enabled: config.notifications.email.enabled,
              recipients: config.notifications.email.recipients
            },
            slack: {
              enabled: config.notifications.slack.enabled
            }
          },
          storage: {
            localPath: config.storage.localPath,
            keepOriginalFiles: config.storage.keepOriginalFiles
          }
        };

        res.json(safeConfig);
      } catch (error) {
        logError(error, { context: 'config endpoint' });
        res.status(500).json({ error: error.message });
      }
    });

    // Atualizar configuração
    router.post('/config', async (req, res) => {
      try {
        // TODO: Implementar atualização de configuração
        // Validar dados recebidos
        // Atualizar arquivo de configuração
        // Reiniciar serviços se necessário
        
        res.json({ message: 'Configuration update not implemented yet' });
      } catch (error) {
        logError(error, { context: 'config update endpoint' });
        res.status(500).json({ error: error.message });
      }
    });

    // Controle do monitor de e-mail
    router.post('/email-monitor/start', async (req, res) => {
      try {
        await this.emailMonitor.start();
        logProcessing('email_monitor_started_via_web', { user: req.ip });
        res.json({ message: 'Email monitor started successfully' });
      } catch (error) {
        logError(error, { context: 'start email monitor endpoint' });
        res.status(500).json({ error: error.message });
      }
    });

    router.post('/email-monitor/stop', async (req, res) => {
      try {
        await this.emailMonitor.stop();
        logProcessing('email_monitor_stopped_via_web', { user: req.ip });
        res.json({ message: 'Email monitor stopped successfully' });
      } catch (error) {
        logError(error, { context: 'stop email monitor endpoint' });
        res.status(500).json({ error: error.message });
      }
    });

    // Teste de notificações
    router.post('/notifications/test', async (req, res) => {
      try {
        const results = await this.notificationService.testAllNotifications();
        res.json(results);
      } catch (error) {
        logError(error, { context: 'test notifications endpoint' });
        res.status(500).json({ error: error.message });
      }
    });

    // Processamento manual de arquivo
    router.post('/process-file', async (req, res) => {
      try {
        const { filePath, options = {} } = req.body;
        
        if (!filePath) {
          return res.status(400).json({ error: 'File path is required' });
        }

        // TODO: Implementar processamento manual
        res.json({ message: 'Manual file processing not implemented yet' });
      } catch (error) {
        logError(error, { context: 'process file endpoint' });
        res.status(500).json({ error: error.message });
      }
    });

    // Listar arquivos
    router.get('/files/:type?', async (req, res) => {
      try {
        const type = req.params.type || 'output';
        const extension = req.query.extension;
        
        const files = await this.storageManager.listFiles(type, extension);
        res.json(files);
      } catch (error) {
        logError(error, { context: 'list files endpoint' });
        res.status(500).json({ error: error.message });
      }
    });

    // Download de arquivo
    router.get('/download/:filename', async (req, res) => {
      try {
        const filename = req.params.filename;
        const filePath = path.join(config.storage.outputPath, filename);
        
        if (!(await fs.pathExists(filePath))) {
          return res.status(404).json({ error: 'File not found' });
        }

        res.download(filePath, filename);
      } catch (error) {
        logError(error, { context: 'download file endpoint' });
        res.status(500).json({ error: error.message });
      }
    });

    // Logs do sistema
    router.get('/logs', async (req, res) => {
      try {
        const logType = req.query.type || 'combined';
        const lines = parseInt(req.query.lines) || 100;
        
        // TODO: Implementar leitura de logs
        res.json({ message: 'Log reading not implemented yet' });
      } catch (error) {
        logError(error, { context: 'logs endpoint' });
        res.status(500).json({ error: error.message });
      }
    });

    // Estatísticas
    router.get('/stats', async (req, res) => {
      try {
        // TODO: Implementar coleta de estatísticas
        const stats = {
          today: {
            processedInvoices: 0,
            generatedFiles: 0,
            errors: 0
          },
          thisWeek: {
            processedInvoices: 0,
            generatedFiles: 0,
            errors: 0
          },
          thisMonth: {
            processedInvoices: 0,
            generatedFiles: 0,
            errors: 0
          }
        };

        res.json(stats);
      } catch (error) {
        logError(error, { context: 'stats endpoint' });
        res.status(500).json({ error: error.message });
      }
    });

    return router;
  }

  /**
   * Inicia o servidor web
   */
  async start() {
    try {
      await new Promise((resolve, reject) => {
        this.server = this.app.listen(this.port, (error) => {
          if (error) {
            reject(error);
          } else {
            resolve();
          }
        });
      });

      logger.info(`Web server started on port ${this.port}`);
      logger.info(`Web interface available at: http://localhost:${this.port}`);
      
      return this.server;
    } catch (error) {
      logError(error, { context: 'start web server' });
      throw error;
    }
  }

  /**
   * Para o servidor web
   */
  async stop() {
    try {
      if (this.server) {
        await new Promise((resolve) => {
          this.server.close(resolve);
        });
        logger.info('Web server stopped');
      }
    } catch (error) {
      logError(error, { context: 'stop web server' });
      throw error;
    }
  }
}

module.exports = WebServer;
