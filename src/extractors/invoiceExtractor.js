const path = require('path');
const fs = require('fs-extra');
const PDFExtractor = require('./pdfExtractor');
const ExcelExtractor = require('./excelExtractor');
const config = require('../config/config');
const { logger, logError, logProcessing } = require('../config/logger');

/**
 * Extrator principal que coordena a extração de dados de invoices
 */
class InvoiceExtractor {
  constructor() {
    this.pdfExtractor = new PDFExtractor();
    this.excelExtractor = new ExcelExtractor();
  }

  /**
   * Processa um arquivo de invoice e extrai dados estruturados
   */
  async processFile(filePath, metadata = {}) {
    try {
      logProcessing('invoice_extraction_started', {
        filePath,
        metadata
      });

      // Verifica se o arquivo existe
      if (!await fs.pathExists(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Determina o tipo de arquivo
      const fileExtension = path.extname(filePath).toLowerCase();
      let extractedData = null;

      switch (fileExtension) {
        case '.pdf':
          extractedData = await this.pdfExtractor.extractInvoiceData(filePath);
          break;
        case '.xls':
        case '.xlsx':
          extractedData = await this.excelExtractor.extractInvoiceData(filePath);
          break;
        default:
          throw new Error(`Unsupported file type: ${fileExtension}`);
      }

      // Enriquece os dados com metadados
      const enrichedData = this.enrichExtractedData(extractedData, metadata, filePath);

      // Salva os dados extraídos
      await this.saveExtractedData(enrichedData, filePath);

      logProcessing('invoice_extraction_completed', {
        filePath,
        extractedData: {
          invoiceNumber: enrichedData.invoiceNumber,
          invoiceDate: enrichedData.invoiceDate,
          vendorName: enrichedData.vendorName,
          total: enrichedData.total,
          lineItemsCount: enrichedData.lineItems.length
        }
      });

      // Dispara a conversão para QuickBooks
      await this.triggerConversion(enrichedData, filePath);

      return enrichedData;
    } catch (error) {
      logError(error, { context: 'processFile', filePath, metadata });
      throw error;
    }
  }

  /**
   * Enriquece os dados extraídos com metadados e validações
   */
  enrichExtractedData(extractedData, metadata, filePath) {
    const enriched = {
      ...extractedData,
      // Metadados do arquivo
      sourceFile: filePath,
      originalFileName: metadata.originalFileName || path.basename(filePath),
      extractionDate: new Date().toISOString(),
      emailMetadata: metadata.emailId ? {
        emailId: metadata.emailId,
        emailSubject: metadata.emailSubject,
        emailFrom: metadata.emailFrom,
        emailDate: metadata.emailDate
      } : null
    };

    // Validações e correções
    if (!enriched.invoiceNumber) {
      enriched.invoiceNumber = this.generateInvoiceNumber(metadata);
    }

    if (!enriched.invoiceDate) {
      enriched.invoiceDate = metadata.emailDate ? 
        new Date(metadata.emailDate).toISOString().split('T')[0] :
        new Date().toISOString().split('T')[0];
    }

    if (!enriched.vendorName) {
      enriched.vendorName = config.quickbooks.defaultVendorName;
    }

    // Calcula totais se não encontrados
    if (!enriched.total && enriched.lineItems.length > 0) {
      enriched.total = enriched.lineItems.reduce((sum, item) => sum + (item.amount || 0), 0);
    }

    if (!enriched.subtotal && enriched.total && enriched.tax) {
      enriched.subtotal = enriched.total - enriched.tax;
    }

    // Adiciona categoria padrão aos itens se não especificada
    enriched.lineItems = enriched.lineItems.map(item => ({
      ...item,
      category: item.category || config.quickbooks.defaultExpenseCategory
    }));

    return enriched;
  }

  /**
   * Gera um número de invoice único se não encontrado
   */
  generateInvoiceNumber(metadata) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const prefix = metadata.emailId ? metadata.emailId.substring(0, 8) : 'AUTO';
    return `${prefix}-${timestamp}`;
  }

  /**
   * Salva os dados extraídos em arquivo JSON
   */
  async saveExtractedData(extractedData, originalFilePath) {
    try {
      const fileName = path.basename(originalFilePath, path.extname(originalFilePath));
      const outputPath = path.join(config.storage.outputPath, `${fileName}_extracted.json`);
      
      await fs.ensureDir(config.storage.outputPath);
      await fs.writeJson(outputPath, extractedData, { spaces: 2 });

      logProcessing('extracted_data_saved', {
        originalFile: originalFilePath,
        outputPath,
        dataSize: JSON.stringify(extractedData).length
      });

      return outputPath;
    } catch (error) {
      logError(error, { context: 'saveExtractedData', originalFilePath });
      throw error;
    }
  }

  /**
   * Dispara a conversão dos dados para formato QuickBooks
   */
  async triggerConversion(extractedData, originalFilePath) {
    try {
      // TODO: Integrar com o módulo de conversão
      logProcessing('conversion_triggered', {
        originalFile: originalFilePath,
        invoiceNumber: extractedData.invoiceNumber,
        total: extractedData.total
      });

      // const converter = require('../converters/quickbooksConverter');
      // await converter.convertToQuickBooks(extractedData);

    } catch (error) {
      logError(error, { context: 'triggerConversion', originalFilePath });
    }
  }

  /**
   * Processa múltiplos arquivos em lote
   */
  async processBatch(filePaths) {
    const results = [];
    
    for (const filePath of filePaths) {
      try {
        const result = await this.processFile(filePath);
        results.push({ filePath, success: true, data: result });
      } catch (error) {
        logError(error, { context: 'processBatch', filePath });
        results.push({ filePath, success: false, error: error.message });
      }
    }

    logProcessing('batch_processing_completed', {
      totalFiles: filePaths.length,
      successCount: results.filter(r => r.success).length,
      errorCount: results.filter(r => !r.success).length
    });

    return results;
  }

  /**
   * Limpa recursos (finaliza workers OCR, etc.)
   */
  async cleanup() {
    try {
      await this.pdfExtractor.terminateOCR();
      logger.info('Invoice extractor cleanup completed');
    } catch (error) {
      logError(error, { context: 'cleanup' });
    }
  }
}

module.exports = InvoiceExtractor;
