const path = require('path');
const fs = require('fs-extra');
const config = require('../config/config');
const { logger, logError, logProcessing } = require('../config/logger');

/**
 * Conversor para formato IIF compatível com QuickBooks Desktop
 */
class IIFConverter {
  constructor() {
    this.outputPath = config.storage.outputPath;
  }

  /**
   * Converte dados de invoice para formato IIF do QuickBooks Desktop
   */
  async convertToIIF(invoiceData, outputFileName = null) {
    try {
      logProcessing('iif_conversion_started', {
        invoiceNumber: invoiceData.invoiceNumber,
        vendorName: invoiceData.vendorName,
        total: invoiceData.total
      });

      // Gera nome do arquivo se não fornecido
      if (!outputFileName) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        outputFileName = `invoice_${invoiceData.invoiceNumber || timestamp}_qbd.iif`;
      }

      const outputFilePath = path.join(this.outputPath, outputFileName);
      await fs.ensureDir(this.outputPath);

      // Gera conteúdo IIF
      const iifContent = this.generateIIFContent(invoiceData);

      // Escreve arquivo IIF
      await fs.writeFile(outputFilePath, iifContent, 'utf8');

      logProcessing('iif_conversion_completed', {
        invoiceNumber: invoiceData.invoiceNumber,
        outputFile: outputFilePath,
        contentLength: iifContent.length
      });

      return outputFilePath;
    } catch (error) {
      logError(error, { context: 'convertToIIF', invoiceData });
      throw error;
    }
  }

  /**
   * Gera o conteúdo do arquivo IIF
   */
  generateIIFContent(invoiceData) {
    const lines = [];

    // Cabeçalho do arquivo IIF
    lines.push('!HDR\tPROD\tVER\tREL\tIIFVER\tDATE\tTIME\tACCNT');
    lines.push('HDR\tQuickBooks Pro\t2023\tR1\t1\t' + this.formatDateForIIF(new Date()) + '\t' + this.formatTimeForIIF(new Date()) + '\tN');

    // Cabeçalhos das transações
    lines.push('!TRNS\tTRNSID\tTRNSTYPE\tDATE\tACCNT\tNAME\tCLASS\tAMOUNT\tDOCNUM\tMEMO\tCLEAR\tTOPRINT\tNAMEADDRLIST\tADDR1\tADDR2\tADDR3\tADDR4\tADDR5\tDUEDATE\tTERMS\tPAID\tSHIPDATE');
    lines.push('!SPL\tSPLID\tTRNSTYPE\tDATE\tACCNT\tNAME\tCLASS\tAMOUNT\tDOCNUM\tMEMO\tCLEAR\tQNTY\tPRICE\tINVITEM\tPAYMETH\tTAXABLE\tREIMBEXP\tSERVICEDATE\tOTHER2');
    lines.push('!ENDTRNS');

    // Dados da transação
    const transactionId = this.generateTransactionId();
    const vendorName = this.sanitizeForIIF(invoiceData.vendorName || config.quickbooks.defaultVendorName);
    const invoiceNumber = this.sanitizeForIIF(invoiceData.invoiceNumber || 'AUTO');
    const invoiceDate = this.formatDateForIIF(invoiceData.invoiceDate);
    const memo = this.sanitizeForIIF(`Angus Sprinkler Repair - ${invoiceNumber}`);

    if (invoiceData.lineItems && invoiceData.lineItems.length > 0) {
      // Múltiplos itens de linha
      let totalAmount = 0;

      // Linha principal da transação (Accounts Payable)
      lines.push(`TRNS\t${transactionId}\tBILL\t${invoiceDate}\tAccounts Payable\t${vendorName}\t\t${this.formatAmountForIIF(invoiceData.total || 0)}\t${invoiceNumber}\t${memo}\tN\tN`);

      // Linhas de detalhamento (uma para cada item)
      invoiceData.lineItems.forEach((item, index) => {
        const itemAmount = item.amount || 0;
        const itemDescription = this.sanitizeForIIF(item.description || 'Service');
        const itemCategory = this.sanitizeForIIF(item.category || config.quickbooks.defaultExpenseCategory);
        
        lines.push(`SPL\t${transactionId}-${index + 1}\tBILL\t${invoiceDate}\t${itemCategory}\t${vendorName}\t\t${this.formatAmountForIIF(-itemAmount)}\t${invoiceNumber}\t${itemDescription}\tN\t${item.quantity || 1}\t${this.formatAmountForIIF(item.rate || itemAmount)}`);
        
        totalAmount += itemAmount;
      });

      // Ajusta se há diferença entre total calculado e total da invoice
      if (invoiceData.total && Math.abs(totalAmount - invoiceData.total) > 0.01) {
        const adjustment = invoiceData.total - totalAmount;
        lines.push(`SPL\t${transactionId}-ADJ\tBILL\t${invoiceDate}\t${config.quickbooks.defaultExpenseCategory}\t${vendorName}\t\t${this.formatAmountForIIF(-adjustment)}\t${invoiceNumber}\tAdjustment\tN\t1\t${this.formatAmountForIIF(adjustment)}`);
      }
    } else {
      // Item único
      const amount = invoiceData.total || 0;
      
      // Linha principal da transação
      lines.push(`TRNS\t${transactionId}\tBILL\t${invoiceDate}\tAccounts Payable\t${vendorName}\t\t${this.formatAmountForIIF(amount)}\t${invoiceNumber}\t${memo}\tN\tN`);
      
      // Linha de detalhamento
      lines.push(`SPL\t${transactionId}-1\tBILL\t${invoiceDate}\t${config.quickbooks.defaultExpenseCategory}\t${vendorName}\t\t${this.formatAmountForIIF(-amount)}\t${invoiceNumber}\t${memo}\tN\t1\t${this.formatAmountForIIF(amount)}`);
    }

    // Finaliza a transação
    lines.push('ENDTRNS');

    return lines.join('\n') + '\n';
  }

  /**
   * Converte para formato de Check (Cheque) em vez de Bill
   */
  async convertToCheckIIF(invoiceData, outputFileName = null) {
    try {
      logProcessing('check_iif_conversion_started', {
        invoiceNumber: invoiceData.invoiceNumber,
        vendorName: invoiceData.vendorName,
        total: invoiceData.total
      });

      if (!outputFileName) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        outputFileName = `check_${invoiceData.invoiceNumber || timestamp}_qbd.iif`;
      }

      const outputFilePath = path.join(this.outputPath, outputFileName);
      await fs.ensureDir(this.outputPath);

      // Gera conteúdo IIF para cheque
      const iifContent = this.generateCheckIIFContent(invoiceData);

      // Escreve arquivo IIF
      await fs.writeFile(outputFilePath, iifContent, 'utf8');

      logProcessing('check_iif_conversion_completed', {
        invoiceNumber: invoiceData.invoiceNumber,
        outputFile: outputFilePath
      });

      return outputFilePath;
    } catch (error) {
      logError(error, { context: 'convertToCheckIIF', invoiceData });
      throw error;
    }
  }

  /**
   * Gera conteúdo IIF para transação de cheque
   */
  generateCheckIIFContent(invoiceData) {
    const lines = [];

    // Cabeçalho
    lines.push('!HDR\tPROD\tVER\tREL\tIIFVER\tDATE\tTIME\tACCNT');
    lines.push('HDR\tQuickBooks Pro\t2023\tR1\t1\t' + this.formatDateForIIF(new Date()) + '\t' + this.formatTimeForIIF(new Date()) + '\tN');

    // Cabeçalhos das transações
    lines.push('!TRNS\tTRNSID\tTRNSTYPE\tDATE\tACCNT\tNAME\tCLASS\tAMOUNT\tDOCNUM\tMEMO\tCLEAR\tTOPRINT');
    lines.push('!SPL\tSPLID\tTRNSTYPE\tDATE\tACCNT\tNAME\tCLASS\tAMOUNT\tDOCNUM\tMEMO\tCLEAR\tQNTY\tPRICE');
    lines.push('!ENDTRNS');

    const transactionId = this.generateTransactionId();
    const vendorName = this.sanitizeForIIF(invoiceData.vendorName || config.quickbooks.defaultVendorName);
    const invoiceNumber = this.sanitizeForIIF(invoiceData.invoiceNumber || 'AUTO');
    const invoiceDate = this.formatDateForIIF(invoiceData.invoiceDate);
    const memo = this.sanitizeForIIF(`Angus Sprinkler Repair - ${invoiceNumber}`);
    const amount = invoiceData.total || 0;

    // Transação principal (débito da conta bancária)
    lines.push(`TRNS\t${transactionId}\tCHECK\t${invoiceDate}\tChecking\t${vendorName}\t\t${this.formatAmountForIIF(-amount)}\t${invoiceNumber}\t${memo}\tN\tN`);

    // Linha de detalhamento (crédito da conta de despesa)
    lines.push(`SPL\t${transactionId}-1\tCHECK\t${invoiceDate}\t${config.quickbooks.defaultExpenseCategory}\t${vendorName}\t\t${this.formatAmountForIIF(amount)}\t${invoiceNumber}\t${memo}\tN\t1\t${this.formatAmountForIIF(amount)}`);

    lines.push('ENDTRNS');

    return lines.join('\n') + '\n';
  }

  /**
   * Gera ID único para transação
   */
  generateTransactionId() {
    return Date.now().toString();
  }

  /**
   * Formata data para IIF (MM/DD/YYYY)
   */
  formatDateForIIF(dateInput) {
    try {
      let date;
      if (typeof dateInput === 'string') {
        date = new Date(dateInput);
      } else {
        date = dateInput;
      }

      if (isNaN(date.getTime())) {
        date = new Date();
      }

      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const year = date.getFullYear();

      return `${month}/${day}/${year}`;
    } catch (error) {
      logError(error, { context: 'formatDateForIIF', dateInput });
      return this.formatDateForIIF(new Date());
    }
  }

  /**
   * Formata hora para IIF (HH:MM:SS)
   */
  formatTimeForIIF(date) {
    try {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${hours}:${minutes}:${seconds}`;
    } catch (error) {
      logError(error, { context: 'formatTimeForIIF', date });
      return '12:00:00';
    }
  }

  /**
   * Formata valor monetário para IIF
   */
  formatAmountForIIF(amount) {
    try {
      if (!amount || isNaN(amount)) {
        return '0.00';
      }
      return parseFloat(amount).toFixed(2);
    } catch (error) {
      logError(error, { context: 'formatAmountForIIF', amount });
      return '0.00';
    }
  }

  /**
   * Sanitiza texto para formato IIF (remove caracteres especiais)
   */
  sanitizeForIIF(text) {
    if (!text) return '';
    
    return text
      .toString()
      .replace(/[\t\n\r]/g, ' ')  // Remove tabs e quebras de linha
      .replace(/"/g, "'")        // Substitui aspas duplas por simples
      .trim()
      .substring(0, 31);         // Limita a 31 caracteres (limite do QuickBooks)
  }

  /**
   * Gera múltiplos formatos IIF
   */
  async generateAllIIFFormats(invoiceData) {
    const results = {};

    try {
      // Formato Bill (Conta a Pagar)
      results.bill = await this.convertToIIF(invoiceData);

      // Formato Check (Cheque)
      results.check = await this.convertToCheckIIF(invoiceData);

      logProcessing('all_iif_formats_generated', {
        invoiceNumber: invoiceData.invoiceNumber,
        formats: Object.keys(results)
      });

      return results;
    } catch (error) {
      logError(error, { context: 'generateAllIIFFormats', invoiceData });
      throw error;
    }
  }
}

module.exports = IIFConverter;
