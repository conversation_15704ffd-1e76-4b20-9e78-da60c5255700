/* Estilos customizados para o Sistema de Automação de Invoices */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: 600;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Dashboard Cards */
.card.text-white .card-title {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.card.text-white h2 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.card.text-white h6 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

/* Service Status */
.service-status {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}

.service-status:last-child {
    margin-bottom: 0;
}

/* Status Badges */
.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #000;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* Tabs */
.nav-tabs .nav-link {
    color: #495057;
    border: none;
    border-bottom: 2px solid transparent;
    background: none;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    border-bottom-color: #dee2e6;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    border-color: transparent;
    border-bottom-color: #0d6efd;
    background: none;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

/* Logs */
.logs-container {
    max-height: 500px;
    overflow-y: auto;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
}

.logs-pre {
    background: none;
    border: none;
    margin: 0;
    padding: 0;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
    color: #495057;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* Forms */
.form-control:focus,
.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-sm {
    font-size: 0.875rem;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* File Actions */
.file-actions {
    white-space: nowrap;
}

.file-actions .btn {
    margin-right: 0.25rem;
}

.file-actions .btn:last-child {
    margin-right: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .card.text-white h2 {
        font-size: 1.5rem;
    }
    
    .service-status {
        margin-bottom: 0.5rem;
    }
    
    .file-actions {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .file-actions .btn {
        margin-right: 0;
        font-size: 0.75rem;
    }
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background-color: #198754;
}

.status-indicator.offline {
    background-color: #dc3545;
}

.status-indicator.warning {
    background-color: #ffc107;
}

/* Progress Bars */
.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
}

.progress-bar {
    border-radius: 0.25rem;
}

/* Custom Scrollbar */
.logs-container::-webkit-scrollbar {
    width: 8px;
}

.logs-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.logs-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.logs-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
