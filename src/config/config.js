const path = require('path');
require('dotenv').config();

/**
 * Configurações centralizadas do sistema
 */
const config = {
  // Configurações gerais
  app: {
    name: 'Invoice Automation System',
    version: '1.0.0',
    env: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT) || 3000,
    logLevel: process.env.LOG_LEVEL || 'info'
  },

  // Configurações do Zoho Mail
  zoho: {
    clientId: process.env.ZOHO_CLIENT_ID,
    clientSecret: process.env.ZOHO_CLIENT_SECRET,
    refreshToken: process.env.ZOHO_REFRESH_TOKEN,
    redirectUri: process.env.ZOHO_REDIRECT_URI || 'http://localhost:3000/auth/zoho/callback',
    emailAddress: process.env.ZOHO_EMAIL_ADDRESS,
    apiBaseUrl: 'https://mail.zoho.com/api'
  },

  // Filtros de e-mail
  emailFilters: {
    senderEmail: process.env.SENDER_EMAIL || '<EMAIL>',
    attachmentKeywords: (process.env.ATTACHMENT_KEYWORDS || 'angus').split(',').map(k => k.trim()),
    supportedFileTypes: (process.env.SUPPORTED_FILE_TYPES || 'pdf,xls,xlsx').split(',').map(t => t.trim()),
    checkInterval: parseInt(process.env.EMAIL_CHECK_INTERVAL) || 5, // minutos
    workingHours: process.env.WORKING_HOURS || '08:00-18:00',
    workingDays: (process.env.WORKING_DAYS || '1,2,3,4,5').split(',').map(d => parseInt(d))
  },

  // Configurações do QuickBooks
  quickbooks: {
    defaultOutputFormat: process.env.DEFAULT_OUTPUT_FORMAT || 'csv', // csv ou iif
    defaultExpenseCategory: process.env.DEFAULT_EXPENSE_CATEGORY || 'Repairs and Maintenance',
    defaultVendorName: process.env.DEFAULT_VENDOR_NAME || 'KeepGreen Irrigation'
  },

  // Configurações de armazenamento
  storage: {
    localPath: process.env.LOCAL_STORAGE_PATH || './data',
    inputPath: path.join(process.env.LOCAL_STORAGE_PATH || './data', 'input'),
    outputPath: path.join(process.env.LOCAL_STORAGE_PATH || './data', 'output'),
    logsPath: path.join(process.env.LOCAL_STORAGE_PATH || './data', 'logs'),
    keepOriginalFiles: process.env.KEEP_ORIGINAL_FILES === 'true',
    
    // Google Drive
    googleDrive: {
      enabled: process.env.GOOGLE_DRIVE_ENABLED === 'true',
      clientId: process.env.GOOGLE_DRIVE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_DRIVE_CLIENT_SECRET,
      refreshToken: process.env.GOOGLE_DRIVE_REFRESH_TOKEN,
      folderId: process.env.GOOGLE_DRIVE_FOLDER_ID
    },

    // OneDrive
    oneDrive: {
      enabled: process.env.ONEDRIVE_ENABLED === 'true',
      clientId: process.env.ONEDRIVE_CLIENT_ID,
      clientSecret: process.env.ONEDRIVE_CLIENT_SECRET,
      refreshToken: process.env.ONEDRIVE_REFRESH_TOKEN
    }
  },

  // Configurações de notificações
  notifications: {
    email: {
      enabled: process.env.EMAIL_NOTIFICATIONS_ENABLED === 'true',
      smtp: {
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT) || 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      },
      recipients: (process.env.NOTIFICATION_EMAILS || '').split(',').map(e => e.trim()).filter(e => e)
    },

    slack: {
      enabled: process.env.SLACK_NOTIFICATIONS_ENABLED === 'true',
      webhookUrl: process.env.SLACK_WEBHOOK_URL
    }
  },

  // Configurações de OCR
  ocr: {
    language: process.env.OCR_LANGUAGE || 'eng',
    psm: parseInt(process.env.OCR_PSM) || 6
  },

  // Configurações de segurança
  security: {
    sessionSecret: process.env.SESSION_SECRET || 'default-secret-change-in-production',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 // MB
  },

  // Configurações de backup e logs
  backup: {
    logRetentionDays: parseInt(process.env.LOG_RETENTION_DAYS) || 30
  },

  // Configurações de debug
  debug: {
    testMode: process.env.TEST_MODE === 'true',
    saveDebugFiles: process.env.SAVE_DEBUG_FILES === 'true'
  }
};

/**
 * Valida se as configurações obrigatórias estão presentes
 */
function validateConfig() {
  const required = [
    'zoho.clientId',
    'zoho.clientSecret',
    'zoho.emailAddress'
  ];

  const missing = [];

  required.forEach(key => {
    const value = key.split('.').reduce((obj, prop) => obj && obj[prop], config);
    if (!value) {
      missing.push(key);
    }
  });

  if (missing.length > 0) {
    throw new Error(`Configurações obrigatórias ausentes: ${missing.join(', ')}`);
  }
}

/**
 * Verifica se está em horário de funcionamento
 */
function isWorkingTime() {
  const now = new Date();
  const dayOfWeek = now.getDay();
  const currentTime = now.getHours() * 100 + now.getMinutes();

  // Verifica dia da semana
  if (!config.emailFilters.workingDays.includes(dayOfWeek)) {
    return false;
  }

  // Verifica horário
  const [startTime, endTime] = config.emailFilters.workingHours.split('-');
  const start = parseInt(startTime.replace(':', ''));
  const end = parseInt(endTime.replace(':', ''));

  return currentTime >= start && currentTime <= end;
}

module.exports = {
  ...config,
  validateConfig,
  isWorkingTime
};
