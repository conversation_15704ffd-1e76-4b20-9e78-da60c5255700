// JavaScript para o Sistema de Automação de Invoices

class InvoiceAutomationApp {
    constructor() {
        this.apiBase = '/api';
        this.refreshInterval = null;
        this.init();
    }

    async init() {
        console.log('Inicializando aplicação...');
        
        // Configura event listeners
        this.setupEventListeners();
        
        // Carrega dados iniciais
        await this.loadInitialData();
        
        // Inicia refresh automático
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // Botões de controle do monitor
        document.getElementById('start-monitor-btn')?.addEventListener('click', () => this.startEmailMonitor());
        document.getElementById('stop-monitor-btn')?.addEventListener('click', () => this.stopEmailMonitor());
        
        // Teste de notificações
        document.getElementById('test-notifications-btn')?.addEventListener('click', () => this.testNotifications());
        
        // Filtros de arquivos
        document.getElementById('file-type-filter')?.addEventListener('change', () => this.refreshFiles());
        document.getElementById('file-search')?.addEventListener('input', () => this.filterFiles());
        
        // Logs
        document.getElementById('log-type-select')?.addEventListener('change', () => this.refreshLogs());
    }

    async loadInitialData() {
        try {
            await Promise.all([
                this.refreshStatus(),
                this.loadConfiguration(),
                this.refreshFiles(),
                this.refreshStats()
            ]);
        } catch (error) {
            console.error('Erro ao carregar dados iniciais:', error);
            this.showAlert('Erro ao carregar dados iniciais', 'danger');
        }
    }

    async refreshStatus() {
        try {
            const response = await fetch(`${this.apiBase}/status`);
            const data = await response.json();
            
            this.updateSystemStatus(data);
            this.updateServiceStatus(data.services);
            
        } catch (error) {
            console.error('Erro ao atualizar status:', error);
            this.updateSystemStatusBadge('Erro', 'danger');
        }
    }

    updateSystemStatus(data) {
        // Atualiza badge do sistema
        const status = data.system.status === 'running' ? 'Online' : 'Offline';
        const badgeClass = data.system.status === 'running' ? 'success' : 'danger';
        this.updateSystemStatusBadge(status, badgeClass);
        
        // Atualiza uptime
        const uptime = this.formatUptime(data.system.uptime);
        document.getElementById('system-uptime').textContent = uptime;
        
        // Atualiza configuração
        const zohoConfigured = data.configuration.zohoMail.configured;
        const zohoStatus = zohoConfigured ? 'Configurado' : 'Não Configurado';
        const zohoClass = zohoConfigured ? 'success' : 'warning';
        document.getElementById('zoho-status').textContent = zohoStatus;
        document.getElementById('zoho-status').className = `badge bg-${zohoClass}`;
    }

    updateServiceStatus(services) {
        // Monitor de e-mail
        const emailMonitorStatus = services.emailMonitor ? 'Ativo' : 'Inativo';
        const emailMonitorClass = services.emailMonitor ? 'success' : 'secondary';
        document.getElementById('email-monitor-status').textContent = emailMonitorStatus;
        document.getElementById('email-monitor-status').className = `badge bg-${emailMonitorClass}`;
        
        // Status detalhado do monitor
        document.getElementById('monitor-status-detail').textContent = emailMonitorStatus;
        document.getElementById('monitor-status-detail').className = `badge bg-${emailMonitorClass}`;
        
        // Notificações
        const notificationsConfigured = services.notifications.email.configured || services.notifications.slack.configured;
        const notificationsStatus = notificationsConfigured ? 'Configurado' : 'Não Configurado';
        const notificationsClass = notificationsConfigured ? 'success' : 'warning';
        document.getElementById('notifications-status').textContent = notificationsStatus;
        document.getElementById('notifications-status').className = `badge bg-${notificationsClass}`;
    }

    updateSystemStatusBadge(text, type) {
        const badge = document.getElementById('system-status');
        if (badge) {
            badge.textContent = text;
            badge.className = `badge bg-${type}`;
        }
    }

    formatUptime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}h ${minutes}m`;
    }

    async startEmailMonitor() {
        try {
            this.setButtonLoading('start-monitor-btn', true);
            
            const response = await fetch(`${this.apiBase}/email-monitor/start`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showAlert('Monitor de e-mail iniciado com sucesso', 'success');
                await this.refreshStatus();
            } else {
                const error = await response.json();
                throw new Error(error.error || 'Erro ao iniciar monitor');
            }
        } catch (error) {
            console.error('Erro ao iniciar monitor:', error);
            this.showAlert(`Erro ao iniciar monitor: ${error.message}`, 'danger');
        } finally {
            this.setButtonLoading('start-monitor-btn', false);
        }
    }

    async stopEmailMonitor() {
        try {
            this.setButtonLoading('stop-monitor-btn', true);
            
            const response = await fetch(`${this.apiBase}/email-monitor/stop`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showAlert('Monitor de e-mail parado com sucesso', 'success');
                await this.refreshStatus();
            } else {
                const error = await response.json();
                throw new Error(error.error || 'Erro ao parar monitor');
            }
        } catch (error) {
            console.error('Erro ao parar monitor:', error);
            this.showAlert(`Erro ao parar monitor: ${error.message}`, 'danger');
        } finally {
            this.setButtonLoading('stop-monitor-btn', false);
        }
    }

    async testNotifications() {
        try {
            this.setButtonLoading('test-notifications-btn', true);
            
            const response = await fetch(`${this.apiBase}/notifications/test`, {
                method: 'POST'
            });
            
            if (response.ok) {
                const results = await response.json();
                let message = 'Teste de notificações concluído:\n';
                
                if (results.email) {
                    message += results.email.error ? `E-mail: Erro - ${results.email.error}\n` : 'E-mail: Sucesso\n';
                }
                
                if (results.slack) {
                    message += results.slack.error ? `Slack: Erro - ${results.slack.error}\n` : 'Slack: Sucesso\n';
                }
                
                const alertType = results.summary.failedTests > 0 ? 'warning' : 'success';
                this.showAlert(message, alertType);
            } else {
                const error = await response.json();
                throw new Error(error.error || 'Erro ao testar notificações');
            }
        } catch (error) {
            console.error('Erro ao testar notificações:', error);
            this.showAlert(`Erro ao testar notificações: ${error.message}`, 'danger');
        } finally {
            this.setButtonLoading('test-notifications-btn', false);
        }
    }

    async refreshFiles() {
        try {
            const typeFilter = document.getElementById('file-type-filter')?.value || '';
            const url = `${this.apiBase}/files/output${typeFilter ? `?extension=${typeFilter}` : ''}`;
            
            const response = await fetch(url);
            const files = await response.json();
            
            this.updateFilesTable(files);
        } catch (error) {
            console.error('Erro ao carregar arquivos:', error);
            this.showAlert('Erro ao carregar arquivos', 'danger');
        }
    }

    updateFilesTable(files) {
        const tbody = document.getElementById('files-table-body');
        if (!tbody) return;
        
        if (files.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center">Nenhum arquivo encontrado</td></tr>';
            return;
        }
        
        tbody.innerHTML = files.map(file => `
            <tr>
                <td>${file.name}</td>
                <td>${this.formatFileSize(file.stats.size)}</td>
                <td>${new Date(file.stats.mtime).toLocaleString('pt-BR')}</td>
                <td class="file-actions">
                    <button class="btn btn-sm btn-primary" onclick="app.downloadFile('${file.name}')">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="app.viewFile('${file.name}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async downloadFile(filename) {
        try {
            window.open(`${this.apiBase}/download/${filename}`, '_blank');
        } catch (error) {
            console.error('Erro ao baixar arquivo:', error);
            this.showAlert('Erro ao baixar arquivo', 'danger');
        }
    }

    async viewFile(filename) {
        // TODO: Implementar visualização de arquivo
        this.showAlert('Visualização de arquivo não implementada ainda', 'info');
    }

    filterFiles() {
        const searchTerm = document.getElementById('file-search')?.value.toLowerCase() || '';
        const rows = document.querySelectorAll('#files-table-body tr');
        
        rows.forEach(row => {
            const filename = row.cells[0]?.textContent.toLowerCase() || '';
            row.style.display = filename.includes(searchTerm) ? '' : 'none';
        });
    }

    async loadConfiguration() {
        try {
            const response = await fetch(`${this.apiBase}/config`);
            const config = await response.json();
            
            this.updateConfigurationForm(config);
        } catch (error) {
            console.error('Erro ao carregar configuração:', error);
            this.showAlert('Erro ao carregar configuração', 'danger');
        }
    }

    updateConfigurationForm(config) {
        // Zoho Mail
        document.getElementById('check-interval-input').value = config.zohoMail.checkInterval || 5;
        document.getElementById('sender-email-input').value = config.zohoMail.emailFilter.senderEmail || '';
        document.getElementById('attachment-keyword-input').value = config.zohoMail.emailFilter.attachmentKeyword || '';
        
        // Notificações
        document.getElementById('email-notifications-enabled').checked = config.notifications.email.enabled;
        document.getElementById('email-recipients').value = config.notifications.email.recipients.join(', ');
        document.getElementById('slack-notifications-enabled').checked = config.notifications.slack.enabled;
        
        // QuickBooks
        document.getElementById('default-format').value = config.quickbooks.defaultOutputFormat || 'csv';
        document.getElementById('default-vendor').value = config.quickbooks.defaultVendorName || '';
        document.getElementById('default-expense-category').value = config.quickbooks.defaultExpenseCategory || '';
        
        // Atualiza informações do monitor
        document.getElementById('check-interval').textContent = `${config.zohoMail.checkInterval} minutos`;
        document.getElementById('target-email').textContent = config.zohoMail.emailFilter.senderEmail || 'Não configurado';
        document.getElementById('keyword-filter').textContent = config.zohoMail.emailFilter.attachmentKeyword || 'Não configurado';
    }

    async refreshStats() {
        try {
            const response = await fetch(`${this.apiBase}/stats`);
            const stats = await response.json();
            
            document.getElementById('invoices-today').textContent = stats.today.processedInvoices || 0;
            document.getElementById('files-generated').textContent = stats.today.generatedFiles || 0;
            document.getElementById('errors-count').textContent = stats.today.errors || 0;
        } catch (error) {
            console.error('Erro ao carregar estatísticas:', error);
        }
    }

    async refreshLogs() {
        try {
            const logType = document.getElementById('log-type-select')?.value || 'combined';
            const response = await fetch(`${this.apiBase}/logs?type=${logType}&lines=100`);
            const data = await response.json();
            
            document.getElementById('logs-content').textContent = data.logs || 'Logs não disponíveis';
        } catch (error) {
            console.error('Erro ao carregar logs:', error);
            document.getElementById('logs-content').textContent = 'Erro ao carregar logs';
        }
    }

    setButtonLoading(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (!button) return;
        
        if (loading) {
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Carregando...';
        } else {
            button.disabled = false;
            // Restaura texto original baseado no ID
            const originalTexts = {
                'start-monitor-btn': 'Iniciar',
                'stop-monitor-btn': 'Parar',
                'test-notifications-btn': 'Testar'
            };
            button.innerHTML = originalTexts[buttonId] || 'Ação';
        }
    }

    showAlert(message, type = 'info') {
        const alertsContainer = document.getElementById('alerts-container');
        if (!alertsContainer) return;
        
        const alertId = 'alert-' + Date.now();
        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message.replace(/\n/g, '<br>')}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        alertsContainer.insertAdjacentHTML('beforeend', alertHtml);
        
        // Remove automaticamente após 5 segundos
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    startAutoRefresh() {
        // Atualiza status a cada 30 segundos
        this.refreshInterval = setInterval(() => {
            this.refreshStatus();
            this.refreshStats();
        }, 30000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
}

// Funções globais para uso nos templates
function refreshStatus() {
    app.refreshStatus();
}

function downloadLogs() {
    app.showAlert('Download de logs não implementado ainda', 'info');
}

function clearCache() {
    app.showAlert('Limpeza de cache não implementada ainda', 'info');
}

function refreshFiles() {
    app.refreshFiles();
}

function refreshLogs() {
    app.refreshLogs();
}

function saveConfiguration() {
    app.showAlert('Salvamento de configuração não implementado ainda', 'info');
}

function loadConfiguration() {
    app.loadConfiguration();
}

// Inicializa a aplicação quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    window.app = new InvoiceAutomationApp();
});
