const ExcelJS = require('exceljs');
const fs = require('fs-extra');
const config = require('../config/config');
const { logger, logError, logProcessing } = require('../config/logger');

/**
 * Extrator de dados de arquivos Excel
 */
class ExcelExtractor {
  constructor() {
    this.workbook = null;
  }

  /**
   * Carrega um arquivo Excel
   */
  async loadFile(filePath) {
    try {
      this.workbook = new ExcelJS.Workbook();
      await this.workbook.xlsx.readFile(filePath);
      
      logProcessing('excel_file_loaded', {
        filePath,
        worksheetCount: this.workbook.worksheets.length
      });
      
      return this.workbook;
    } catch (error) {
      logError(error, { context: 'loadFile', filePath });
      throw error;
    }
  }

  /**
   * Extrai dados estruturados de uma invoice em Excel
   */
  async extractInvoiceData(filePath) {
    try {
      await this.loadFile(filePath);
      
      const invoiceData = {
        invoiceNumber: null,
        invoiceDate: null,
        vendorName: null,
        lineItems: [],
        subtotal: null,
        tax: null,
        total: null,
        rawData: null
      };

      // Processa a primeira planilha (assumindo que a invoice está lá)
      const worksheet = this.workbook.worksheets[0];
      if (!worksheet) {
        throw new Error('No worksheet found in Excel file');
      }

      logProcessing('excel_extraction_started', {
        filePath,
        worksheetName: worksheet.name,
        rowCount: worksheet.rowCount,
        columnCount: worksheet.columnCount
      });

      // Converte planilha para array de dados
      const rawData = this.worksheetToArray(worksheet);
      invoiceData.rawData = rawData;

      // Extrai campos específicos
      invoiceData.invoiceNumber = this.findInvoiceNumber(rawData);
      invoiceData.invoiceDate = this.findInvoiceDate(rawData);
      invoiceData.vendorName = this.findVendorName(rawData);
      invoiceData.lineItems = this.findLineItems(rawData);
      invoiceData.subtotal = this.findSubtotal(rawData);
      invoiceData.tax = this.findTax(rawData);
      invoiceData.total = this.findTotal(rawData);

      // Usa valores padrão se não encontrados
      if (!invoiceData.vendorName) {
        invoiceData.vendorName = config.quickbooks.defaultVendorName;
      }

      if (!invoiceData.invoiceDate) {
        invoiceData.invoiceDate = new Date().toISOString().split('T')[0];
      }

      logProcessing('excel_extraction_completed', {
        filePath,
        invoiceData: {
          invoiceNumber: invoiceData.invoiceNumber,
          invoiceDate: invoiceData.invoiceDate,
          vendorName: invoiceData.vendorName,
          lineItemsCount: invoiceData.lineItems.length,
          total: invoiceData.total
        }
      });

      return invoiceData;
    } catch (error) {
      logError(error, { context: 'extractInvoiceData', filePath });
      throw error;
    }
  }

  /**
   * Converte uma planilha Excel para array de dados
   */
  worksheetToArray(worksheet) {
    const data = [];
    
    worksheet.eachRow((row, rowNumber) => {
      const rowData = [];
      row.eachCell((cell, colNumber) => {
        rowData[colNumber - 1] = this.getCellValue(cell);
      });
      data[rowNumber - 1] = rowData;
    });

    return data;
  }

  /**
   * Obtém o valor de uma célula, tratando diferentes tipos
   */
  getCellValue(cell) {
    if (!cell || cell.value === null || cell.value === undefined) {
      return '';
    }

    // Trata diferentes tipos de valor
    if (typeof cell.value === 'object') {
      if (cell.value.formula) {
        return cell.value.result || '';
      }
      if (cell.value.text) {
        return cell.value.text;
      }
      if (cell.value instanceof Date) {
        return cell.value.toISOString().split('T')[0];
      }
    }

    return cell.value.toString();
  }

  /**
   * Busca o número da invoice nos dados
   */
  findInvoiceNumber(data) {
    const patterns = [
      /invoice\s*#?\s*:?\s*([A-Z0-9\-]+)/i,
      /inv\s*#?\s*:?\s*([A-Z0-9\-]+)/i,
      /number\s*:?\s*([A-Z0-9\-]+)/i
    ];

    return this.findValueByPatterns(data, patterns);
  }

  /**
   * Busca a data da invoice nos dados
   */
  findInvoiceDate(data) {
    const patterns = [
      /date\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/i,
      /invoice\s*date\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/i
    ];

    const dateString = this.findValueByPatterns(data, patterns);
    if (dateString) {
      return this.formatDate(dateString);
    }

    // Procura por células que contenham datas
    for (const row of data) {
      for (const cell of row) {
        if (this.isDateString(cell)) {
          return this.formatDate(cell);
        }
      }
    }

    return null;
  }

  /**
   * Busca o nome do fornecedor nos dados
   */
  findVendorName(data) {
    const patterns = [
      /from\s*:?\s*([^\n\r]+)/i,
      /vendor\s*:?\s*([^\n\r]+)/i,
      /bill\s*to\s*:?\s*([^\n\r]+)/i
    ];

    return this.findValueByPatterns(data, patterns);
  }

  /**
   * Busca itens de linha nos dados
   */
  findLineItems(data) {
    const lineItems = [];
    
    try {
      // Procura por cabeçalhos de tabela
      let headerRow = -1;
      const headers = ['description', 'qty', 'quantity', 'rate', 'price', 'amount', 'total'];
      
      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        const rowText = row.join(' ').toLowerCase();
        
        if (headers.some(header => rowText.includes(header))) {
          headerRow = i;
          break;
        }
      }

      if (headerRow >= 0) {
        // Processa linhas após o cabeçalho
        for (let i = headerRow + 1; i < data.length; i++) {
          const row = data[i];
          const item = this.parseLineItemRow(row);
          
          if (item && item.description && item.amount > 0) {
            lineItems.push(item);
          }
        }
      }

      // Se não encontrou itens, cria um item genérico
      if (lineItems.length === 0) {
        lineItems.push({
          description: 'Angus Sprinkler Repair',
          quantity: 1,
          rate: 0,
          amount: 0
        });
      }

    } catch (error) {
      logError(error, { context: 'findLineItems' });
    }

    return lineItems;
  }

  /**
   * Analisa uma linha para extrair um item
   */
  parseLineItemRow(row) {
    try {
      const item = {
        description: '',
        quantity: 1,
        rate: 0,
        amount: 0
      };

      // Procura por descrição (primeira célula não vazia)
      for (const cell of row) {
        if (cell && cell.toString().trim() && !this.isNumeric(cell)) {
          item.description = cell.toString().trim();
          break;
        }
      }

      // Procura por valores numéricos
      const numbers = row.filter(cell => this.isNumeric(cell)).map(cell => parseFloat(cell));
      
      if (numbers.length >= 1) {
        item.amount = numbers[numbers.length - 1]; // Último número é geralmente o total
      }
      
      if (numbers.length >= 2) {
        item.rate = numbers[numbers.length - 2]; // Penúltimo é geralmente o preço
      }
      
      if (numbers.length >= 3) {
        item.quantity = numbers[0]; // Primeiro é geralmente a quantidade
      }

      return item.description ? item : null;
    } catch (error) {
      logError(error, { context: 'parseLineItemRow', row });
      return null;
    }
  }

  /**
   * Busca o subtotal nos dados
   */
  findSubtotal(data) {
    const patterns = [
      /subtotal\s*:?\s*\$?(\d+\.?\d*)/i,
      /sub\s*total\s*:?\s*\$?(\d+\.?\d*)/i
    ];

    const value = this.findValueByPatterns(data, patterns);
    return value ? parseFloat(value.replace(/[^\d.]/g, '')) : null;
  }

  /**
   * Busca o valor do imposto nos dados
   */
  findTax(data) {
    const patterns = [
      /tax\s*:?\s*\$?(\d+\.?\d*)/i,
      /sales\s*tax\s*:?\s*\$?(\d+\.?\d*)/i
    ];

    const value = this.findValueByPatterns(data, patterns);
    return value ? parseFloat(value.replace(/[^\d.]/g, '')) : null;
  }

  /**
   * Busca o total nos dados
   */
  findTotal(data) {
    const patterns = [
      /total\s*:?\s*\$?(\d+\.?\d*)/i,
      /amount\s*due\s*:?\s*\$?(\d+\.?\d*)/i,
      /grand\s*total\s*:?\s*\$?(\d+\.?\d*)/i
    ];

    const value = this.findValueByPatterns(data, patterns);
    return value ? parseFloat(value.replace(/[^\d.]/g, '')) : null;
  }

  /**
   * Busca um valor usando padrões regex nos dados
   */
  findValueByPatterns(data, patterns) {
    for (const row of data) {
      for (const cell of row) {
        if (!cell) continue;
        
        const cellText = cell.toString();
        for (const pattern of patterns) {
          const match = cellText.match(pattern);
          if (match && match[1]) {
            return match[1].trim();
          }
        }
      }
    }
    return null;
  }

  /**
   * Verifica se um valor é numérico
   */
  isNumeric(value) {
    return !isNaN(parseFloat(value)) && isFinite(value);
  }

  /**
   * Verifica se uma string parece ser uma data
   */
  isDateString(value) {
    if (!value) return false;
    const datePattern = /\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}/;
    return datePattern.test(value.toString());
  }

  /**
   * Formata data para padrão consistente
   */
  formatDate(dateString) {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        // Tenta outros formatos
        const parts = dateString.split(/[\/\-]/);
        if (parts.length === 3) {
          let [month, day, year] = parts.map(p => parseInt(p));
          
          if (year < 100) {
            year += 2000;
          }
          
          return new Date(year, month - 1, day).toISOString().split('T')[0];
        }
      }
      return date.toISOString().split('T')[0];
    } catch (error) {
      logError(error, { context: 'formatDate', dateString });
      return new Date().toISOString().split('T')[0];
    }
  }
}

module.exports = ExcelExtractor;
