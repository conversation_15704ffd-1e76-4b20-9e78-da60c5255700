# Guia de Testes - Sistema de Automação de Invoices

Este documento descreve como executar e interpretar os testes do sistema de automação de invoices.

## 🧪 Tipos de Testes

### Testes Unitários
Testam componentes individuais isoladamente:
- Configuração do sistema
- Conversores QuickBooks (CSV/IIF)
- Extratores de dados (PDF/Excel)
- Serviços de notificação
- Gerenciamento de armazenamento

### Testes de Integração
Testam a interação entre componentes:
- Fluxo completo de processamento
- Integração com APIs externas
- Sistema de arquivos
- Servidor web e endpoints

## 🚀 Executando Testes

### Comandos Básicos

```bash
# Todos os testes
npm test

# Testes com relatório de cobertura
npm run test:coverage

# Apenas testes unitários
npm run test:unit

# Apenas testes de integração
npm run test:integration

# Modo watch (re-executa ao salvar)
npm run test:watch

# Para CI/CD
npm run test:ci
```

### Configuração de Ambiente

Os testes usam um ambiente isolado com:
- Variáveis de ambiente específicas para teste
- Diretórios temporários
- Mocks para APIs externas
- Configurações de teste no `jest.config.js`

## 📊 Cobertura de Código

### Métricas de Cobertura

O sistema mantém as seguintes metas de cobertura:
- **Linhas**: 70%
- **Funções**: 70%
- **Branches**: 70%
- **Statements**: 70%

### Relatórios

```bash
# Gerar relatório HTML
npm run test:coverage

# Visualizar relatório
open coverage/lcov-report/index.html
```

## 🔧 Estrutura de Testes

### Diretórios

```
tests/
├── unit/                 # Testes unitários
│   ├── config.test.js    # Configuração
│   ├── quickbooksConverter.test.js
│   ├── pdfExtractor.test.js
│   └── ...
├── integration/          # Testes de integração
│   ├── system.test.js    # Sistema completo
│   ├── api.test.js       # Endpoints web
│   └── ...
├── fixtures/             # Dados de teste
│   ├── sample-invoice.pdf
│   ├── sample-invoice.csv
│   └── ...
├── setup.js              # Configuração global
├── globalSetup.js        # Setup antes de todos os testes
└── globalTeardown.js     # Cleanup após todos os testes
```

### Utilitários de Teste

O arquivo `tests/setup.js` fornece utilitários globais:

```javascript
// Criar dados mock
const invoice = global.testUtils.createMockInvoice({
  invoiceNumber: 'TEST-001',
  amount: 100.50
});

// Criar arquivo de teste
const filePath = await global.testUtils.createTestFile('test.pdf', content);

// Aguardar tempo específico
await global.testUtils.wait(1000);
```

## 📝 Escrevendo Testes

### Exemplo de Teste Unitário

```javascript
const { describe, it, expect, beforeEach } = require('@jest/globals');
const QuickBooksConverter = require('../../src/converters/quickbooksConverter');

describe('QuickBooksConverter', () => {
  let converter;

  beforeEach(() => {
    converter = new QuickBooksConverter();
  });

  describe('CSV Conversion', () => {
    it('should convert invoice data to CSV format', async () => {
      const invoiceData = global.testUtils.createMockInvoice();
      
      const result = await converter.convertToQuickBooks(invoiceData, {
        format: 'csv',
        outputPath: global.testUtils.getTestDataDir()
      });

      expect(result.success).toBe(true);
      expect(result.files.csv).toBeDefined();
    });
  });
});
```

### Exemplo de Teste de Integração

```javascript
const { describe, it, expect, beforeAll, afterAll } = require('@jest/globals');
const InvoiceAutomationSystem = require('../../src/index');

describe('System Integration', () => {
  let system;

  beforeAll(async () => {
    system = new InvoiceAutomationSystem();
    await system.initialize();
  });

  afterAll(async () => {
    if (system.isRunning) {
      await system.stop();
    }
  });

  it('should start and stop system successfully', async () => {
    await system.start();
    expect(system.isRunning).toBe(true);
    
    await system.stop();
    expect(system.isRunning).toBe(false);
  });
});
```

## 🔍 Debugging Testes

### Logs Detalhados

```bash
# Habilitar logs durante testes
JEST_VERBOSE=true npm test

# Debug específico
DEBUG=test:* npm test
```

### Executar Teste Específico

```bash
# Arquivo específico
npm test -- tests/unit/config.test.js

# Teste específico por nome
npm test -- --testNamePattern="should validate email format"

# Suíte específica
npm test -- --testPathPattern="unit"
```

## 🚨 Troubleshooting

### Problemas Comuns

1. **Testes falhando por timeout**
   ```bash
   # Aumentar timeout
   npm test -- --testTimeout=60000
   ```

2. **Problemas de permissão de arquivo**
   ```bash
   # Limpar diretórios de teste
   rm -rf tests/temp test-data
   ```

3. **Mocks não funcionando**
   ```javascript
   // Limpar mocks entre testes
   afterEach(() => {
     jest.clearAllMocks();
   });
   ```

### Limpeza Manual

```bash
# Remover arquivos de teste
npm run test:clean

# Ou manualmente
rm -rf coverage/ test-data/ tests/temp/
```

## 📈 Métricas e Relatórios

### Relatórios JUnit (CI/CD)

Os testes geram relatórios JUnit em `coverage/junit.xml` para integração com sistemas de CI/CD.

### Relatórios de Cobertura

- **HTML**: `coverage/lcov-report/index.html`
- **LCOV**: `coverage/lcov.info`
- **JSON**: `coverage/coverage-final.json`

## 🔄 Integração Contínua

### GitHub Actions

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '16'
      - run: npm ci
      - run: npm run test:ci
      - uses: codecov/codecov-action@v3
```

### Scripts de CI

```bash
# Instalar dependências
npm ci

# Executar testes com cobertura
npm run test:ci

# Verificar qualidade do código
npm run lint
```

## 📚 Recursos Adicionais

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Testing Best Practices](https://github.com/goldbergyoni/javascript-testing-best-practices)
- [Node.js Testing Guide](https://nodejs.org/en/docs/guides/testing/)

---

**Mantenha os testes atualizados e execute-os regularmente para garantir a qualidade do código!**
