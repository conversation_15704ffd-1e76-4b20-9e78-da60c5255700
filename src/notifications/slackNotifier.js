const axios = require('axios');
const config = require('../config/config');
const { logger, logError, logProcessing } = require('../config/logger');

/**
 * Serviço de notificações via Slack
 */
class SlackNotifier {
  constructor() {
    this.webhookUrl = config.notifications.slack.webhookUrl;
    this.isConfigured = config.notifications.slack.enabled && this.webhookUrl;
  }

  /**
   * Envia notificação de invoice processada para o Slack
   */
  async sendInvoiceProcessedNotification(invoiceData, conversionResults) {
    try {
      if (!this.isConfigured) {
        logger.warn('Slack notifier not configured, skipping notification');
        return;
      }

      const message = this.buildInvoiceProcessedMessage(invoiceData, conversionResults);
      const result = await this.sendSlackMessage(message);

      logProcessing('slack_invoice_notification_sent', {
        invoiceNumber: invoiceData.invoiceNumber,
        webhookUrl: this.webhookUrl.substring(0, 50) + '...'
      });

      return result;
    } catch (error) {
      logError(error, { context: 'sendInvoiceProcessedNotification', invoiceData });
      throw error;
    }
  }

  /**
   * Envia notificação de erro para o Slack
   */
  async sendErrorNotification(error, context = {}) {
    try {
      if (!this.isConfigured) {
        logger.warn('Slack notifier not configured, skipping error notification');
        return;
      }

      const message = this.buildErrorMessage(error, context);
      const result = await this.sendSlackMessage(message);

      logProcessing('slack_error_notification_sent', {
        error: error.message,
        context
      });

      return result;
    } catch (notificationError) {
      logError(notificationError, { context: 'sendErrorNotification', originalError: error.message });
    }
  }

  /**
   * Envia relatório diário para o Slack
   */
  async sendDailyReport(reportData) {
    try {
      if (!this.isConfigured) {
        logger.warn('Slack notifier not configured, skipping daily report');
        return;
      }

      const message = this.buildDailyReportMessage(reportData);
      const result = await this.sendSlackMessage(message);

      logProcessing('slack_daily_report_sent', {
        reportData: {
          processedInvoices: reportData.processedInvoices,
          errors: reportData.errors,
          totalFiles: reportData.totalFiles
        }
      });

      return result;
    } catch (error) {
      logError(error, { context: 'sendDailyReport', reportData });
      throw error;
    }
  }

  /**
   * Constrói mensagem para invoice processada
   */
  buildInvoiceProcessedMessage(invoiceData, conversionResults) {
    const filesGenerated = [];
    
    if (conversionResults.conversions.csv) {
      Object.keys(conversionResults.conversions.csv).forEach(type => {
        filesGenerated.push(`• ${type.toUpperCase()} CSV`);
      });
    }
    
    if (conversionResults.conversions.iif) {
      Object.keys(conversionResults.conversions.iif).forEach(type => {
        filesGenerated.push(`• ${type.toUpperCase()} IIF`);
      });
    }

    const statusColor = conversionResults.summary.errors.length > 0 ? 'warning' : 'good';
    const statusIcon = conversionResults.summary.errors.length > 0 ? '⚠️' : '✅';

    return {
      text: `${statusIcon} Invoice Processada`,
      attachments: [
        {
          color: statusColor,
          title: `Invoice ${invoiceData.invoiceNumber || 'N/A'}`,
          fields: [
            {
              title: 'Fornecedor',
              value: invoiceData.vendorName || 'N/A',
              short: true
            },
            {
              title: 'Valor Total',
              value: `$${invoiceData.total || '0.00'}`,
              short: true
            },
            {
              title: 'Data',
              value: invoiceData.invoiceDate || 'N/A',
              short: true
            },
            {
              title: 'Arquivos Gerados',
              value: `${conversionResults.summary.totalFiles} arquivos`,
              short: true
            },
            {
              title: 'Formatos',
              value: filesGenerated.join('\n') || 'Nenhum',
              short: false
            }
          ],
          footer: 'Sistema de Automação de Invoices',
          ts: Math.floor(Date.now() / 1000)
        }
      ]
    };
  }

  /**
   * Constrói mensagem de erro
   */
  buildErrorMessage(error, context) {
    return {
      text: '❌ Erro no Sistema de Automação',
      attachments: [
        {
          color: 'danger',
          title: 'Detalhes do Erro',
          fields: [
            {
              title: 'Mensagem',
              value: error.message,
              short: false
            },
            {
              title: 'Tipo',
              value: error.name || 'Error',
              short: true
            },
            {
              title: 'Data/Hora',
              value: new Date().toLocaleString('pt-BR'),
              short: true
            }
          ],
          footer: 'Sistema de Automação de Invoices',
          ts: Math.floor(Date.now() / 1000)
        }
      ]
    };
  }

  /**
   * Constrói mensagem de relatório diário
   */
  buildDailyReportMessage(reportData) {
    const date = new Date().toLocaleDateString('pt-BR');
    const successRate = reportData.successRate || '0%';
    const statusIcon = reportData.errors > 0 ? '⚠️' : '📊';

    return {
      text: `${statusIcon} Relatório Diário - ${date}`,
      attachments: [
        {
          color: reportData.errors > 0 ? 'warning' : 'good',
          title: 'Resumo do Dia',
          fields: [
            {
              title: 'Invoices Processadas',
              value: reportData.processedInvoices || 0,
              short: true
            },
            {
              title: 'Arquivos Gerados',
              value: reportData.totalFiles || 0,
              short: true
            },
            {
              title: 'Erros',
              value: reportData.errors || 0,
              short: true
            },
            {
              title: 'Taxa de Sucesso',
              value: successRate,
              short: true
            }
          ],
          footer: 'Sistema de Automação de Invoices',
          ts: Math.floor(Date.now() / 1000)
        }
      ]
    };
  }

  /**
   * Envia mensagem para o Slack via webhook
   */
  async sendSlackMessage(message) {
    try {
      const response = await axios.post(this.webhookUrl, message, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error) {
      logError(error, { context: 'sendSlackMessage', message });
      throw error;
    }
  }

  /**
   * Testa a configuração do Slack
   */
  async testSlackConfiguration() {
    try {
      if (!this.isConfigured) {
        throw new Error('Slack notifier is not configured');
      }

      const testMessage = {
        text: '🧪 Teste - Sistema de Automação de Invoices',
        attachments: [
          {
            color: 'good',
            title: 'Teste de Configuração',
            text: 'Este é um teste para verificar a configuração do Slack.',
            footer: 'Sistema de Automação de Invoices',
            ts: Math.floor(Date.now() / 1000)
          }
        ]
      };

      const result = await this.sendSlackMessage(testMessage);
      
      logger.info('Test Slack message sent successfully');
      return result;
    } catch (error) {
      logError(error, { context: 'testSlackConfiguration' });
      throw error;
    }
  }
}

module.exports = SlackNotifier;
